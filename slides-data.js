// Slide data for the presentation
const slidesData = [
    {
        id: 1,
        title: "Title Slide",
        content: `
            <div class="slide-container">
                <!-- Floating background icons -->
                <div class="floating-icon icon-1"><i class="fas fa-cogs"></i></div>
                <div class="floating-icon icon-2"><i class="fas fa-lightbulb"></i></div>
                <div class="floating-icon icon-3"><i class="fas fa-users"></i></div>
                <div class="floating-icon icon-4"><i class="fas fa-rocket"></i></div>
                
                <!-- Energy pulse effect -->
                <div class="energy-pulse" style="top: 20%; left: 20%;"></div>
                <div class="energy-pulse" style="bottom: 20%; right: 20%; animation-delay: 2s;"></div>
                
                <div class="title-content">
                    <h1 class="main-title">Breaking the Feature Factory</h1>
                    <p class="subtitle">Build What Actually Matters</p>
                    
                    <div class="event-info">
                        <div class="event-text">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            Friends of Figma • Abu Dhabi • 2025
                        </div>
                    </div>
                    
                    <div class="speaker-info">
                        <div class="speaker-text">
                            <i class="fas fa-user mr-2"></i>
                            Puravi • Senior Product Leader • Veteran of Many Mistakes
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 2,
        title: "The Problem",
        content: `
            <div class="slide-container">
                <div class="content-grid">
                    <div>
                        <h1 class="problem-main-title">We're All Building the Wrong Things</h1>
                        <p class="problem-text">
                            Every day, product teams around the world ship features that nobody uses. 
                            We're busy, we're productive, we're hitting our deadlines... 
                            <span class="highlight">but are we actually helping anyone?</span>
                        </p>
                        
                        <div class="scenario-card">
                            <div class="scenario-title">
                                <i class="fas fa-exclamation-triangle mr-3"></i>
                                Sound Familiar?
                            </div>
                            <div class="scenario-text">
                                "We shipped 47 features last quarter! Our velocity is amazing!"<br><br>
                                <em>Meanwhile, users are still struggling with the same basic problems they had 6 months ago...</em>
                            </div>
                        </div>
                        
                        <div class="scenario-card">
                            <div class="scenario-title">
                                <i class="fas fa-clock mr-3"></i>
                                The Daily Reality
                            </div>
                            <div class="scenario-text">
                                Sprint planning feels like a feature wishlist. Stakeholders keep asking for "just one more thing." 
                                Your backlog is 200 items long, but you can't remember the last time a user said "wow, this changed my life."
                            </div>
                        </div>
                    </div>
                    
                    <div class="visual-section">
                        <div class="factory-visual">
                            <div class="factory-icon">
                                <i class="fas fa-industry"></i>
                            </div>
                            <div class="factory-title">The Feature Factory</div>
                            <div class="factory-subtitle">Optimized for output, not outcomes</div>
                        </div>
                        
                        <div class="stats-row">
                            <div class="stat-box">
                                <div class="stat-number">80%</div>
                                <div class="stat-label">Features rarely used</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number">3x</div>
                                <div class="stat-label">More features than needed</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 3,
        title: "Speaker Intro",
        content: `
            <div class="slide-container">
                <div class="intro-content">
                    <h1 class="intro-main-title">Hi, I'm Puravi</h1>
                    <p class="intro-subtitle">Senior Product Leader & Recovering Feature Factory Addict</p>
                    
                    <div class="intro-grid">
                        <div class="intro-card">
                            <div class="intro-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="intro-text">
                                <h3>10+ Years</h3>
                                <p>Building products at startups and enterprises</p>
                            </div>
                        </div>
                        
                        <div class="intro-card">
                            <div class="intro-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="intro-text">
                                <h3>Countless Mistakes</h3>
                                <p>Shipped features nobody wanted or used</p>
                            </div>
                        </div>
                        
                        <div class="intro-card">
                            <div class="intro-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="intro-text">
                                <h3>Hard-Won Lessons</h3>
                                <p>Learned what actually drives user value</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="intro-quote">
                        <p>"I've been where you are. I've felt the pressure to ship, ship, ship. 
                        And I've learned the hard way that being busy doesn't mean being effective."</p>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 4,
        title: "Quick Poll",
        content: `
            <div class="slide-container">
                <!-- Floating emojis for engagement -->
                <div class="floating-emoji emoji-1">🤔</div>
                <div class="floating-emoji emoji-2">📊</div>
                <div class="floating-emoji emoji-3">🚀</div>
                <div class="floating-emoji emoji-4">💡</div>
                
                <h1 class="poll-main-title">Quick Reality Check</h1>
                
                <div class="poll-container">
                    <div class="poll-question">
                        How confident are you that your team is building the right things?
                    </div>
                    
                    <div class="poll-instruction">
                        Raise your hand for the option that best describes your current situation
                    </div>
                    
                    <div class="poll-options">
                        <div class="poll-option">
                            <div class="option-letter">A</div>
                            <div class="option-text">
                                Very confident - we validate everything with users and measure impact
                            </div>
                        </div>
                        
                        <div class="poll-option">
                            <div class="option-letter">B</div>
                            <div class="option-text">
                                Somewhat confident - we do research but still ship features that flop
                            </div>
                        </div>
                        
                        <div class="poll-option">
                            <div class="option-letter">C</div>
                            <div class="option-text">
                                Not very confident - we're mostly guessing and hoping for the best
                            </div>
                        </div>
                        
                        <div class="poll-option">
                            <div class="option-letter">D</div>
                            <div class="option-text">
                                What's confidence? We just build whatever stakeholders ask for
                            </div>
                        </div>
                    </div>
                    
                    <div class="engagement-note">
                        <div class="engagement-text">
                            Don't worry - if you picked C or D, you're in good company. 
                            Most product teams are flying blind, and that's exactly what we're here to fix!
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 5,
        title: "Factory Signs",
        content: `
            <div class="slide-container">
                <h1 class="factory-signs-title">Warning Signs You're in a Feature Factory</h1>
                <p class="factory-signs-subtitle">How many of these sound familiar?</p>

                <div class="warning-signs">
                    <div class="sign-card">
                        <div class="sign-icon">📊</div>
                        <div class="sign-title">Metrics Obsession</div>
                        <div class="sign-description">You celebrate shipping 50 features but can't name 3 user problems you solved</div>
                    </div>

                    <div class="sign-card">
                        <div class="sign-icon">🏃‍♂️</div>
                        <div class="sign-title">Always Rushing</div>
                        <div class="sign-description">"We don't have time for user research" is your team's unofficial motto</div>
                    </div>

                    <div class="sign-card">
                        <div class="sign-icon">📝</div>
                        <div class="sign-title">Endless Backlog</div>
                        <div class="sign-description">Your backlog has 200+ items and grows faster than you can ship</div>
                    </div>

                    <div class="sign-card">
                        <div class="sign-icon">🤷‍♀️</div>
                        <div class="sign-title">Feature Amnesia</div>
                        <div class="sign-description">You shipped something 3 months ago and nobody remembers why</div>
                    </div>

                    <div class="sign-card">
                        <div class="sign-icon">😤</div>
                        <div class="sign-title">Stakeholder Whiplash</div>
                        <div class="sign-description">Every executive has a "quick win" that becomes a 3-month project</div>
                    </div>

                    <div class="sign-card">
                        <div class="sign-icon">🎯</div>
                        <div class="sign-title">Success Theater</div>
                        <div class="sign-description">Your success metrics look great but users are still frustrated</div>
                    </div>
                </div>

                <div class="bottom-section">
                    <div class="bottom-title">
                        <span class="emoji">🚨</span>
                        If 3+ of these sound familiar, you're in a feature factory
                    </div>
                    <div class="bottom-text">
                        Don't worry - <span class="highlight">every successful product team</span> has been here.
                        The good news? There's a way out, and it's easier than you think.
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 6,
        title: "Real Cost",
        content: `
            <div class="slide-container">
                <div class="content-grid">
                    <div>
                        <h1 class="cost-main-title">The Hidden Price We Pay</h1>
                        <p class="cost-intro-text">
                            Feature factories aren't just inefficient - they're <span class="highlight">expensive</span>.
                            Here's what it really costs when we build the wrong things.
                        </p>

                        <div class="cost-examples">
                            <div class="cost-item">
                                <div class="cost-title">
                                    <span class="emoji">💸</span>
                                    Wasted Development Time
                                </div>
                                <div class="cost-description">
                                    6 months building a feature that 5% of users actually need
                                </div>
                            </div>

                            <div class="cost-item">
                                <div class="cost-title">
                                    <span class="emoji">😫</span>
                                    User Frustration
                                </div>
                                <div class="cost-description">
                                    Cluttered interfaces that make simple tasks harder than they should be
                                </div>
                            </div>

                            <div class="cost-item">
                                <div class="cost-title">
                                    <span class="emoji">🔧</span>
                                    Technical Debt
                                </div>
                                <div class="cost-description">
                                    Maintaining features nobody uses while real problems go unfixed
                                </div>
                            </div>

                            <div class="cost-item">
                                <div class="cost-title">
                                    <span class="emoji">🔄</span>
                                    Opportunity Cost
                                </div>
                                <div class="cost-description">
                                    Missing the chance to build something that could actually change users' lives
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="visual-section">
                        <div class="price-tag">
                            <div class="price-amount">$2.3M</div>
                            <div class="price-label">Annual cost per 100-person team</div>
                        </div>

                        <div class="impact-stats">
                            <div class="stat-box">
                                <div class="stat-number">80%</div>
                                <div class="stat-label">Features rarely used</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number">60%</div>
                                <div class="stat-label">Time on wrong priorities</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number">3x</div>
                                <div class="stat-label">Longer to find solutions</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number">40%</div>
                                <div class="stat-label">Higher team turnover</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 7,
        title: "Case Study",
        content: `
            <div class="slide-container">
                <h1 class="case-study-title">A $2M Lesson in Assumptions</h1>
                <p class="case-study-subtitle">Sometimes the best way to learn is through spectacular failure</p>

                <div class="story-container">
                    <div class="story-header">
                        <div class="story-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="story-title">A True Story from My Past</div>
                    </div>

                    <div class="story-content">
                        Our sales team asked for "better reporting." Simple enough, right?
                        Six months later, we had built a dashboard with <span class="highlight">47 different widgets</span>,
                        12 chart types, and enough filters to make a NASA engineer weep.
                    </div>

                    <div class="story-stats">
                        <div class="story-stat">
                            <div class="stat-number">$2M</div>
                            <div class="stat-label">Development cost</div>
                        </div>
                        <div class="story-stat">
                            <div class="stat-number">6</div>
                            <div class="stat-label">Months wasted</div>
                        </div>
                        <div class="story-stat">
                            <div class="stat-number">12%</div>
                            <div class="stat-label">Adoption rate</div>
                        </div>
                    </div>

                    <div class="story-content">
                        <strong>The plot twist?</strong> After talking to actual salespeople, we discovered they just wanted to know
                        <span class="highlight">which leads were getting cold</span>. A simple email notification solved their real problem
                        in 2 days, not 6 months.
                    </div>
                </div>

                <div class="lesson-learned">
                    <div class="lesson-title">
                        <span class="emoji">💡</span>
                        The Lesson
                    </div>
                    <div class="lesson-text">
                        We spent 6 months building what we <em>thought</em> they needed,
                        instead of 6 minutes asking what they <em>actually</em> needed.
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 8,
        title: "Why We Fall",
        content: `
            <div class="slide-container">
                <div class="content-grid">
                    <div>
                        <h1 class="why-fall-title">Why Smart People Build Dumb Things</h1>
                        <p class="why-fall-intro">
                            It's not about intelligence - it's about <span class="highlight">incentives, pressure, and human psychology</span>.
                            Here's why even brilliant teams fall into the feature factory trap.
                        </p>

                        <div class="reasons-list">
                            <div class="reason-item">
                                <div class="reason-icon">⏰</div>
                                <div class="reason-content">
                                    <div class="reason-title">Pressure to Ship</div>
                                    <div class="reason-text">"Just get something out there" becomes the default response to every problem</div>
                                </div>
                            </div>

                            <div class="reason-item">
                                <div class="reason-icon">📈</div>
                                <div class="reason-content">
                                    <div class="reason-title">Vanity Metrics</div>
                                    <div class="reason-text">We measure what's easy (features shipped) not what matters (problems solved)</div>
                                </div>
                            </div>

                            <div class="reason-item">
                                <div class="reason-icon">🎭</div>
                                <div class="reason-content">
                                    <div class="reason-title">HiPPO Syndrome</div>
                                    <div class="reason-text">Highest Paid Person's Opinion wins, even when data says otherwise</div>
                                </div>
                            </div>

                            <div class="reason-item">
                                <div class="reason-icon">🔮</div>
                                <div class="reason-content">
                                    <div class="reason-title">Crystal Ball Thinking</div>
                                    <div class="reason-text">We assume we know what users want without actually asking them</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="psychology-section">
                        <div class="psychology-title">The Psychology Behind It</div>

                        <div class="psychology-facts">
                            <div class="fact-box">
                                <div class="fact-title">Confirmation Bias</div>
                                <div class="fact-text">We see what we want to see in user feedback</div>
                            </div>
                            <div class="fact-box">
                                <div class="fact-title">Sunk Cost Fallacy</div>
                                <div class="fact-text">"We've already built 80%, might as well finish"</div>
                            </div>
                            <div class="fact-box">
                                <div class="fact-title">Planning Fallacy</div>
                                <div class="fact-text">Everything takes longer than we think it will</div>
                            </div>
                            <div class="fact-box">
                                <div class="fact-title">Feature Creep</div>
                                <div class="fact-text">"While we're at it, let's add just one more thing..."</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 9,
        title: "Solution Framework",
        content: `
            <div class="slide-container">
                <h1 class="framework-title">The Value Engine Blueprint</h1>
                <p class="framework-subtitle">Three simple pillars to transform how you build products</p>

                <div class="pillars-container">
                    <div class="pillar-card">
                        <div class="pillar-number">1</div>
                        <div class="pillar-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="pillar-title">Ask Better Questions</div>
                        <div class="pillar-description">
                            Stop building what people ask for. Start solving what they actually need.
                        </div>
                    </div>

                    <div class="pillar-card">
                        <div class="pillar-number">2</div>
                        <div class="pillar-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <div class="pillar-title">Build Smart, Not Fast</div>
                        <div class="pillar-description">
                            Test your assumptions before you build. Measure what matters, not what's easy.
                        </div>
                    </div>

                    <div class="pillar-card">
                        <div class="pillar-number">3</div>
                        <div class="pillar-icon">
                            <i class="fas fa-sync"></i>
                        </div>
                        <div class="pillar-title">Never Stop Improving</div>
                        <div class="pillar-description">
                            Launch is just the beginning. Keep optimizing based on real user behavior.
                        </div>
                    </div>
                </div>

                <div class="transformation-arrow">
                    <div class="arrow-content">
                        <div class="arrow-text">From Feature Factory</div>
                        <div class="arrow-icon">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="arrow-text">To Value Engine</div>
                    </div>
                </div>

                <div class="bottom-message">
                    <div class="bottom-text">
                        <span class="highlight">The best part?</span> You don't need to transform everything overnight.
                        Start with one pillar, see the results, then expand.
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 10,
        title: "Pillar Discovery",
        content: `
            <div class="slide-container">
                <h1 class="pillar-detail-title">Pillar 1: Ask Better Questions</h1>
                <p class="pillar-detail-subtitle">The art of uncovering what people actually need</p>

                <div class="discovery-content">
                    <div class="discovery-section">
                        <div class="discovery-header">
                            <div class="discovery-icon">❌</div>
                            <div class="discovery-label">Stop Asking</div>
                        </div>
                        <div class="discovery-items">
                            <div class="discovery-item">"What features do you want?"</div>
                            <div class="discovery-item">"How would you solve this?"</div>
                            <div class="discovery-item">"What's missing from our product?"</div>
                        </div>
                    </div>

                    <div class="discovery-section">
                        <div class="discovery-header">
                            <div class="discovery-icon">✅</div>
                            <div class="discovery-label">Start Asking</div>
                        </div>
                        <div class="discovery-items">
                            <div class="discovery-item">"What's the hardest part of your day?"</div>
                            <div class="discovery-item">"When do you feel most frustrated?"</div>
                            <div class="discovery-item">"What would make you look like a hero?"</div>
                        </div>
                    </div>
                </div>

                <div class="technique-box">
                    <div class="technique-title">
                        <span class="emoji">🎯</span>
                        The 5-Why Technique
                    </div>
                    <div class="technique-example">
                        <div class="why-item">
                            <strong>User:</strong> "I want better reporting"
                        </div>
                        <div class="why-item">
                            <strong>Why?</strong> "To track my team's performance"
                        </div>
                        <div class="why-item">
                            <strong>Why?</strong> "To know if we're hitting our goals"
                        </div>
                        <div class="why-item">
                            <strong>Why?</strong> "To avoid missing our quarterly targets"
                        </div>
                        <div class="why-item">
                            <strong>Why?</strong> "Because missing targets affects my bonus"
                        </div>
                        <div class="why-item">
                            <strong>Why?</strong> "Because I need to know about problems before my boss does"
                        </div>
                    </div>
                    <div class="technique-insight">
                        <strong>Real need:</strong> Early warning system for potential problems
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 11,
        title: "Interactive Exercise",
        content: `
            <div class="slide-container">
                <h1 class="exercise-title">Let's Practice Together</h1>
                <p class="exercise-subtitle">Turn this feature request into a real user need</p>

                <div class="exercise-scenario">
                    <div class="scenario-header">
                        <div class="scenario-icon">📧</div>
                        <div class="scenario-title">The Request</div>
                    </div>
                    <div class="scenario-content">
                        "We need a bulk email feature so users can send messages to multiple contacts at once."
                    </div>
                </div>

                <div class="exercise-questions">
                    <div class="question-section">
                        <div class="question-title">🤔 Questions to Ask</div>
                        <div class="question-list">
                            <div class="question-item">What are you trying to accomplish?</div>
                            <div class="question-item">How are you doing this today?</div>
                            <div class="question-item">What's frustrating about the current process?</div>
                            <div class="question-item">How often does this happen?</div>
                            <div class="question-item">What would success look like?</div>
                        </div>
                    </div>

                    <div class="discovery-section">
                        <div class="discovery-title">💡 What We Might Discover</div>
                        <div class="discovery-options">
                            <div class="discovery-option">
                                <div class="option-title">Option A: Time Saver</div>
                                <div class="option-text">They're copying/pasting the same message 50 times</div>
                            </div>
                            <div class="discovery-option">
                                <div class="option-title">Option B: Relationship Manager</div>
                                <div class="option-text">They need to nurture leads with personalized sequences</div>
                            </div>
                            <div class="discovery-option">
                                <div class="option-title">Option C: Compliance Issue</div>
                                <div class="option-text">They need to ensure everyone gets the same legal notice</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="exercise-takeaway">
                    <div class="takeaway-title">
                        <span class="emoji">🎯</span>
                        The Point
                    </div>
                    <div class="takeaway-text">
                        Same request, three completely different solutions.
                        <span class="highlight">Always dig deeper</span> before you start building.
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 12,
        title: "Pillar Development",
        content: `
            <div class="slide-container">
                <h1 class="pillar-detail-title">Pillar 2: Build Smart, Not Fast</h1>
                <p class="pillar-detail-subtitle">Test assumptions before you commit resources</p>

                <div class="smart-build-content">
                    <div class="build-principle">
                        <div class="principle-icon">🧪</div>
                        <div class="principle-title">Start with the Smallest Test</div>
                        <div class="principle-description">
                            Before building anything, find the cheapest way to validate your assumption
                        </div>
                    </div>

                    <div class="testing-ladder">
                        <div class="ladder-title">The Testing Ladder</div>
                        <div class="ladder-steps">
                            <div class="ladder-step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <div class="step-title">Talk to Users</div>
                                    <div class="step-description">5 conversations can save 5 months</div>
                                    <div class="step-cost">Cost: $0 | Time: 1 day</div>
                                </div>
                            </div>

                            <div class="ladder-step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <div class="step-title">Create a Mockup</div>
                                    <div class="step-description">Show, don't tell. Get reactions to visuals</div>
                                    <div class="step-cost">Cost: $500 | Time: 3 days</div>
                                </div>
                            </div>

                            <div class="ladder-step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <div class="step-title">Build a Prototype</div>
                                    <div class="step-description">Clickable demo with core functionality</div>
                                    <div class="step-cost">Cost: $5K | Time: 2 weeks</div>
                                </div>
                            </div>

                            <div class="ladder-step">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <div class="step-title">MVP Launch</div>
                                    <div class="step-description">Minimum viable version to real users</div>
                                    <div class="step-cost">Cost: $50K | Time: 2 months</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="smart-metrics">
                        <div class="metrics-title">Measure What Matters</div>
                        <div class="metrics-comparison">
                            <div class="metrics-column">
                                <div class="metrics-header bad">❌ Vanity Metrics</div>
                                <div class="metrics-list">
                                    <div class="metric-item">Features shipped</div>
                                    <div class="metric-item">Lines of code</div>
                                    <div class="metric-item">Story points completed</div>
                                </div>
                            </div>
                            <div class="metrics-column">
                                <div class="metrics-header good">✅ Value Metrics</div>
                                <div class="metrics-list">
                                    <div class="metric-item">User problems solved</div>
                                    <div class="metric-item">Time saved per user</div>
                                    <div class="metric-item">User satisfaction increase</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 13,
        title: "Pillar Optimization",
        content: `
            <div class="slide-container">
                <h1 class="pillar-detail-title">Pillar 3: Never Stop Improving</h1>
                <p class="pillar-detail-subtitle">Launch is just the beginning of the journey</p>

                <div class="optimization-content">
                    <div class="optimization-cycle">
                        <div class="cycle-title">The Continuous Improvement Cycle</div>
                        <div class="cycle-steps">
                            <div class="cycle-step">
                                <div class="step-icon">📊</div>
                                <div class="step-title">Measure</div>
                                <div class="step-description">Track real user behavior, not just usage stats</div>
                            </div>
                            <div class="cycle-arrow">→</div>
                            <div class="cycle-step">
                                <div class="step-icon">🔍</div>
                                <div class="step-title">Analyze</div>
                                <div class="step-description">Find patterns in what's working and what isn't</div>
                            </div>
                            <div class="cycle-arrow">→</div>
                            <div class="cycle-step">
                                <div class="step-icon">💡</div>
                                <div class="step-title">Hypothesize</div>
                                <div class="step-description">Form theories about how to improve</div>
                            </div>
                            <div class="cycle-arrow">→</div>
                            <div class="cycle-step">
                                <div class="step-icon">🧪</div>
                                <div class="step-title">Test</div>
                                <div class="step-description">Run small experiments to validate ideas</div>
                            </div>
                        </div>
                    </div>

                    <div class="optimization-examples">
                        <div class="example-title">Real Optimization Wins</div>
                        <div class="example-grid">
                            <div class="example-card">
                                <div class="example-metric">+40%</div>
                                <div class="example-description">Task completion rate after simplifying the checkout flow</div>
                            </div>
                            <div class="example-card">
                                <div class="example-metric">-60%</div>
                                <div class="example-description">Support tickets after improving error messages</div>
                            </div>
                            <div class="example-card">
                                <div class="example-metric">+25%</div>
                                <div class="example-description">User retention after personalizing onboarding</div>
                            </div>
                        </div>
                    </div>

                    <div class="optimization-mindset">
                        <div class="mindset-title">
                            <span class="emoji">🎯</span>
                            The Optimization Mindset
                        </div>
                        <div class="mindset-text">
                            Every feature is a hypothesis. Every launch is an experiment.
                            <span class="highlight">Every user interaction is data</span> that can make your product better.
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 14,
        title: "Making It Stick",
        content: `
            <div class="slide-container">
                <h1 class="stick-title">Making the Change Stick</h1>
                <p class="stick-subtitle">How to transform your team culture, not just your process</p>

                <div class="change-strategy">
                    <div class="strategy-section">
                        <div class="strategy-title">Start Small, Think Big</div>
                        <div class="strategy-steps">
                            <div class="strategy-step">
                                <div class="step-number">Week 1</div>
                                <div class="step-content">
                                    <div class="step-title">Pick One Feature Request</div>
                                    <div class="step-description">Apply the 5-why technique to understand the real need</div>
                                </div>
                            </div>
                            <div class="strategy-step">
                                <div class="step-number">Week 2</div>
                                <div class="step-content">
                                    <div class="step-title">Talk to 3 Users</div>
                                    <div class="step-description">Validate your understanding before building anything</div>
                                </div>
                            </div>
                            <div class="strategy-step">
                                <div class="step-number">Week 3</div>
                                <div class="step-content">
                                    <div class="step-title">Create a Simple Test</div>
                                    <div class="step-description">Mockup, prototype, or even a manual process</div>
                                </div>
                            </div>
                            <div class="strategy-step">
                                <div class="step-number">Week 4</div>
                                <div class="step-content">
                                    <div class="step-title">Measure the Impact</div>
                                    <div class="step-description">Did it actually solve the user's problem?</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="culture-changes">
                        <div class="culture-title">Cultural Shifts to Encourage</div>
                        <div class="culture-grid">
                            <div class="culture-item">
                                <div class="culture-icon">🗣️</div>
                                <div class="culture-text">
                                    <strong>Celebrate Learning</strong><br>
                                    Reward teams for discovering what doesn't work
                                </div>
                            </div>
                            <div class="culture-item">
                                <div class="culture-icon">❓</div>
                                <div class="culture-text">
                                    <strong>Question Everything</strong><br>
                                    Make "Why are we building this?" a standard question
                                </div>
                            </div>
                            <div class="culture-item">
                                <div class="culture-icon">👥</div>
                                <div class="culture-text">
                                    <strong>User Empathy</strong><br>
                                    Bring real user voices into every planning meeting
                                </div>
                            </div>
                            <div class="culture-item">
                                <div class="culture-icon">🔄</div>
                                <div class="culture-text">
                                    <strong>Iteration Over Perfection</strong><br>
                                    Ship to learn, not to impress
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="stick-reminder">
                    <div class="reminder-title">
                        <span class="emoji">⚡</span>
                        Remember
                    </div>
                    <div class="reminder-text">
                        Culture change takes time. Start with one person, one feature, one success story.
                        <span class="highlight">Progress, not perfection.</span>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 15,
        title: "Your Plan",
        content: `
            <div class="slide-container">
                <h1 class="plan-title">Your 30-Day Action Plan</h1>
                <p class="plan-subtitle">Ready to break free from the feature factory? Here's how to start</p>

                <div class="action-plan">
                    <div class="plan-week">
                        <div class="week-header">
                            <div class="week-number">Week 1</div>
                            <div class="week-title">Audit Your Current State</div>
                        </div>
                        <div class="week-tasks">
                            <div class="task-item">📋 List your last 10 shipped features</div>
                            <div class="task-item">📊 Check usage data for each one</div>
                            <div class="task-item">🎯 Identify which solved real user problems</div>
                            <div class="task-item">💡 Calculate your "value hit rate"</div>
                        </div>
                    </div>

                    <div class="plan-week">
                        <div class="week-header">
                            <div class="week-number">Week 2</div>
                            <div class="week-title">Start Asking Better Questions</div>
                        </div>
                        <div class="week-tasks">
                            <div class="task-item">🗣️ Pick one current feature request</div>
                            <div class="task-item">❓ Apply the 5-why technique</div>
                            <div class="task-item">👥 Talk to 3 users about their real needs</div>
                            <div class="task-item">📝 Document what you discover</div>
                        </div>
                    </div>

                    <div class="plan-week">
                        <div class="week-header">
                            <div class="week-number">Week 3</div>
                            <div class="week-title">Test Before You Build</div>
                        </div>
                        <div class="week-tasks">
                            <div class="task-item">🎨 Create a mockup or prototype</div>
                            <div class="task-item">👀 Show it to real users</div>
                            <div class="task-item">📊 Measure their reactions</div>
                            <div class="task-item">🔄 Iterate based on feedback</div>
                        </div>
                    </div>

                    <div class="plan-week">
                        <div class="week-header">
                            <div class="week-number">Week 4</div>
                            <div class="week-title">Share Your Success</div>
                        </div>
                        <div class="week-tasks">
                            <div class="task-item">📈 Present your findings to the team</div>
                            <div class="task-item">🎉 Celebrate what you learned</div>
                            <div class="task-item">🔄 Apply the process to your next feature</div>
                            <div class="task-item">📚 Start building your value engine</div>
                        </div>
                    </div>
                </div>

                <div class="plan-resources">
                    <div class="resources-title">
                        <span class="emoji">🛠️</span>
                        Tools to Help You Succeed
                    </div>
                    <div class="resources-list">
                        <div class="resource-item">📋 User interview templates</div>
                        <div class="resource-item">📊 Value metrics tracking sheet</div>
                        <div class="resource-item">🎯 Feature prioritization framework</div>
                        <div class="resource-item">📖 Recommended reading list</div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 16,
        title: "Let's Connect",
        content: `
            <div class="slide-container">
                <!-- Floating background elements -->
                <div class="floating-elements">
                    <div class="floating-icon icon-1"><i class="fas fa-lightbulb"></i></div>
                    <div class="floating-icon icon-2"><i class="fas fa-users"></i></div>
                    <div class="floating-icon icon-3"><i class="fas fa-rocket"></i></div>
                    <div class="floating-icon icon-4"><i class="fas fa-heart"></i></div>
                </div>

                <div class="connect-content">
                    <h1 class="connect-title">Let's Keep Building Better</h1>
                    <p class="connect-subtitle">The conversation doesn't end here</p>

                    <div class="connect-grid">
                        <div class="connect-card">
                            <div class="connect-icon">
                                <i class="fab fa-linkedin"></i>
                            </div>
                            <div class="connect-info">
                                <div class="connect-platform">LinkedIn</div>
                                <div class="connect-handle">@puravi-shah</div>
                                <div class="connect-description">Connect for product insights and career tips</div>
                            </div>
                        </div>

                        <div class="connect-card">
                            <div class="connect-icon">
                                <i class="fab fa-twitter"></i>
                            </div>
                            <div class="connect-info">
                                <div class="connect-platform">Twitter</div>
                                <div class="connect-handle">@puravi_builds</div>
                                <div class="connect-description">Daily thoughts on product strategy</div>
                            </div>
                        </div>

                        <div class="connect-card">
                            <div class="connect-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="connect-info">
                                <div class="connect-platform">Email</div>
                                <div class="connect-handle"><EMAIL></div>
                                <div class="connect-description">Questions about your transformation journey</div>
                            </div>
                        </div>

                        <div class="connect-card">
                            <div class="connect-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div class="connect-info">
                                <div class="connect-platform">Website</div>
                                <div class="connect-handle">productvalue.co</div>
                                <div class="connect-description">Free resources and case studies</div>
                            </div>
                        </div>
                    </div>

                    <div class="final-message">
                        <div class="message-title">
                            <span class="emoji">🚀</span>
                            One Last Thing
                        </div>
                        <div class="message-text">
                            You have everything you need to start building better products.
                            The only question is: <span class="highlight">will you take the first step?</span>
                        </div>
                    </div>

                    <div class="thank-you">
                        <div class="thank-title">Thank You!</div>
                        <div class="thank-subtitle">Questions?</div>
                    </div>
                </div>
            </div>
        `
    }
];

// Function to generate slides
function generateSlides() {
    const slidesContainer = document.getElementById('slides-container');
    slidesContainer.innerHTML = '';

    slidesData.forEach((slideData, index) => {
        const slideElement = document.createElement('div');
        slideElement.className = `slide ${index === 0 ? 'active' : ''}`;
        slideElement.id = `slide-${slideData.id}`;
        slideElement.innerHTML = slideData.content;
        slidesContainer.appendChild(slideElement);
    });
}
