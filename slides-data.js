// Slide data for the presentation
const slidesData = [
    {
        id: 1,
        title: "Title Slide",
        content: `
            <div class="slide-container">
                <!-- Floating background icons -->
                <div class="floating-icon icon-1"><i class="fas fa-cogs"></i></div>
                <div class="floating-icon icon-2"><i class="fas fa-lightbulb"></i></div>
                <div class="floating-icon icon-3"><i class="fas fa-users"></i></div>
                <div class="floating-icon icon-4"><i class="fas fa-rocket"></i></div>
                
                <!-- Energy pulse effect -->
                <div class="energy-pulse" style="top: 20%; left: 20%;"></div>
                <div class="energy-pulse" style="bottom: 20%; right: 20%; animation-delay: 2s;"></div>
                
                <div class="title-content">
                    <h1 class="main-title">Breaking the Feature Factory</h1>
                    <p class="subtitle">Build What Actually Matters</p>
                    
                    <div class="event-info">
                        <div class="event-text">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            Friends of Figma • Abu Dhabi • 2025
                        </div>
                    </div>
                    
                    <div class="speaker-info">
                        <div class="speaker-text">
                            <i class="fas fa-user mr-2"></i>
                            Puravi • Senior Product Leader • Veteran of Many Mistakes
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 2,
        title: "The Problem",
        content: `
            <div class="slide-container">
                <div class="content-grid">
                    <div>
                        <h1 class="problem-main-title">We're All Building the Wrong Things</h1>
                        <p class="problem-text">
                            Every day, product teams around the world ship features that nobody uses. 
                            We're busy, we're productive, we're hitting our deadlines... 
                            <span class="highlight">but are we actually helping anyone?</span>
                        </p>
                        
                        <div class="scenario-card">
                            <div class="scenario-title">
                                <i class="fas fa-exclamation-triangle mr-3"></i>
                                Sound Familiar?
                            </div>
                            <div class="scenario-text">
                                "We shipped 47 features last quarter! Our velocity is amazing!"<br><br>
                                <em>Meanwhile, users are still struggling with the same basic problems they had 6 months ago...</em>
                            </div>
                        </div>
                        
                        <div class="scenario-card">
                            <div class="scenario-title">
                                <i class="fas fa-clock mr-3"></i>
                                The Daily Reality
                            </div>
                            <div class="scenario-text">
                                Sprint planning feels like a feature wishlist. Stakeholders keep asking for "just one more thing." 
                                Your backlog is 200 items long, but you can't remember the last time a user said "wow, this changed my life."
                            </div>
                        </div>
                    </div>
                    
                    <div class="visual-section">
                        <div class="factory-visual">
                            <div class="factory-icon">
                                <i class="fas fa-industry"></i>
                            </div>
                            <div class="factory-title">The Feature Factory</div>
                            <div class="factory-subtitle">Optimized for output, not outcomes</div>
                        </div>
                        
                        <div class="stats-row">
                            <div class="stat-box">
                                <div class="stat-number">80%</div>
                                <div class="stat-label">Features rarely used</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number">3x</div>
                                <div class="stat-label">More features than needed</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 3,
        title: "Speaker Intro",
        content: `
            <div class="slide-container">
                <div class="intro-content">
                    <h1 class="intro-main-title">Hi, I'm Puravi</h1>
                    <p class="intro-subtitle">Senior Product Leader & Recovering Feature Factory Addict</p>
                    
                    <div class="intro-grid">
                        <div class="intro-card">
                            <div class="intro-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="intro-text">
                                <h3>10+ Years</h3>
                                <p>Building products at startups and enterprises</p>
                            </div>
                        </div>
                        
                        <div class="intro-card">
                            <div class="intro-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="intro-text">
                                <h3>Countless Mistakes</h3>
                                <p>Shipped features nobody wanted or used</p>
                            </div>
                        </div>
                        
                        <div class="intro-card">
                            <div class="intro-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="intro-text">
                                <h3>Hard-Won Lessons</h3>
                                <p>Learned what actually drives user value</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="intro-quote">
                        <p>"I've been where you are. I've felt the pressure to ship, ship, ship. 
                        And I've learned the hard way that being busy doesn't mean being effective."</p>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 4,
        title: "Quick Poll",
        content: `
            <div class="slide-container">
                <!-- Floating emojis for engagement -->
                <div class="floating-emoji emoji-1">🤔</div>
                <div class="floating-emoji emoji-2">📊</div>
                <div class="floating-emoji emoji-3">🚀</div>
                <div class="floating-emoji emoji-4">💡</div>
                
                <h1 class="poll-main-title">Quick Reality Check</h1>
                
                <div class="poll-container">
                    <div class="poll-question">
                        How confident are you that your team is building the right things?
                    </div>
                    
                    <div class="poll-instruction">
                        Raise your hand for the option that best describes your current situation
                    </div>
                    
                    <div class="poll-options">
                        <div class="poll-option">
                            <div class="option-letter">A</div>
                            <div class="option-text">
                                Very confident - we validate everything with users and measure impact
                            </div>
                        </div>
                        
                        <div class="poll-option">
                            <div class="option-letter">B</div>
                            <div class="option-text">
                                Somewhat confident - we do research but still ship features that flop
                            </div>
                        </div>
                        
                        <div class="poll-option">
                            <div class="option-letter">C</div>
                            <div class="option-text">
                                Not very confident - we're mostly guessing and hoping for the best
                            </div>
                        </div>
                        
                        <div class="poll-option">
                            <div class="option-letter">D</div>
                            <div class="option-text">
                                What's confidence? We just build whatever stakeholders ask for
                            </div>
                        </div>
                    </div>
                    
                    <div class="engagement-note">
                        <div class="engagement-text">
                            Don't worry - if you picked C or D, you're in good company. 
                            Most product teams are flying blind, and that's exactly what we're here to fix!
                        </div>
                    </div>
                </div>
            </div>
        `
    }
];

// Function to generate slides
function generateSlides() {
    const slidesContainer = document.getElementById('slides-container');
    slidesContainer.innerHTML = '';
    
    slidesData.forEach((slideData, index) => {
        const slideElement = document.createElement('div');
        slideElement.className = `slide ${index === 0 ? 'active' : ''}`;
        slideElement.id = `slide-${slideData.id}`;
        slideElement.innerHTML = slideData.content;
        slidesContainer.appendChild(slideElement);
    });
}
