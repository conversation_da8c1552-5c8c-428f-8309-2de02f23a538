<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Breaking the Feature Factory - Slideshow</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', sans-serif;
            overflow: hidden;
            transition: background-color 0.3s ease;
        }

        .slideshow-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 80px 20px 120px 20px;
            box-sizing: border-box;
        }

        .slide-wrapper {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .slide {
            display: none;
            width: 100%;
            height: 100%;
            max-width: 1280px;
            max-height: 100%;
            overflow: visible;
            position: relative;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .slide-container {
            width: 100%;
            max-width: 1280px;
            min-height: 720px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            font-family: 'Inter', sans-serif;
            padding: 60px;
            box-sizing: border-box;
            transform-origin: center center;
        }

        /* Top Controls */
        .top-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Navigation Bar */
        .nav-container {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
        }

        .nav-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 15px 25px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 10px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-size: 16px;
            min-width: 45px;
            text-align: center;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .slide-indicator {
            color: white;
            font-weight: 500;
            font-size: 14px;
            padding: 0 15px;
        }

        /* Dark theme background */
        body.dark {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        }

        body.dark .slide-container {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        }

        /* Light theme background */
        body.light {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        body.light .slide-container {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        body.light .control-btn,
        body.light .nav-bar,
        body.light .nav-btn {
            background: rgba(0, 0, 0, 0.1);
            border-color: rgba(0, 0, 0, 0.2);
            color: #1e293b;
        }

        body.light .control-btn:hover,
        body.light .nav-btn:hover {
            background: rgba(0, 0, 0, 0.2);
        }

        body.light .slide-indicator {
            color: #1e293b;
        }

        /* Responsive scaling */
        @media (max-width: 1400px) {
            .slide-container {
                transform: scale(0.9);
            }
        }

        @media (max-width: 1200px) {
            .slide-container {
                transform: scale(0.8);
            }
        }

        @media (max-width: 1000px) {
            .slide-container {
                transform: scale(0.7);
            }
        }

        @media (max-width: 800px) {
            .slide-container {
                transform: scale(0.6);
                padding: 40px;
            }

            .nav-bar {
                padding: 12px 20px;
                gap: 15px;
            }

            .nav-btn {
                padding: 8px 12px;
                font-size: 14px;
                min-width: 40px;
            }

            .control-btn {
                width: 45px;
                height: 45px;
                font-size: 16px;
            }

            .top-controls {
                gap: 8px;
            }
        }
    </style>
    <link rel="stylesheet" href="slide-styles.css">
</head>

<body class="dark">
    <!-- Top Controls -->
    <div class="top-controls">
        <div class="control-btn" onclick="toggleFullscreen()" title="Toggle Fullscreen">
            <i class="fas fa-expand" id="fullscreen-icon"></i>
        </div>
        <div class="control-btn" onclick="toggleTheme()" title="Toggle Theme">
            <i class="fas fa-sun" id="theme-icon"></i>
        </div>
    </div>

    <!-- Slideshow Container -->
    <div class="slideshow-container">
        <div class="slide-wrapper">
            <div id="slides-container"></div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="nav-container">
        <div class="nav-bar">
            <button class="nav-btn" onclick="previousSlide()" id="prev-btn">
                <i class="fas fa-chevron-left"></i>
            </button>

            <div class="slide-indicator">
                <span id="current-slide">1</span> / <span id="total-slides">16</span>
            </div>

            <button class="nav-btn" onclick="nextSlide()" id="next-btn">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>

    <script src="slides-data.js"></script>
    <script>
        let currentSlide = 1;
        let totalSlides = 16;
        let currentTheme = 'dark';

        // Initialize slideshow
        function init() {
            generateSlides();
            totalSlides = slidesData.length;
            document.getElementById('total-slides').textContent = totalSlides;
            showSlide(currentSlide);
            updateNavigation();

            // Keyboard navigation
            document.addEventListener('keydown', handleKeyPress);

            // Handle window resize
            window.addEventListener('resize', handleResize);
        }

        // Handle window resize
        function handleResize() {
            // Slides will auto-scale with CSS media queries
        }

        // Show specific slide
        function showSlide(slideNumber) {
            // Hide all slides
            const allSlides = document.querySelectorAll('.slide');
            allSlides.forEach(slide => slide.classList.remove('active'));

            // Show current slide
            const currentSlideElement = document.getElementById(`slide-${slideNumber}`);
            if (currentSlideElement) {
                currentSlideElement.classList.add('active');
            }

            updateNavigation();
        }

        // Navigation functions
        function nextSlide() {
            if (currentSlide < totalSlides) {
                currentSlide++;
                showSlide(currentSlide);
            }
        }

        function previousSlide() {
            if (currentSlide > 1) {
                currentSlide--;
                showSlide(currentSlide);
            }
        }

        function goToSlide(slideNumber) {
            currentSlide = slideNumber;
            showSlide(currentSlide);
        }

        // Update navigation state
        function updateNavigation() {
            document.getElementById('current-slide').textContent = currentSlide;
            document.getElementById('total-slides').textContent = totalSlides;

            // Update buttons
            document.getElementById('prev-btn').disabled = currentSlide === 1;
            document.getElementById('next-btn').disabled = currentSlide === totalSlides;
        }

        // Theme toggle
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');

            if (currentTheme === 'dark') {
                currentTheme = 'light';
                body.className = 'light';
                themeIcon.className = 'fas fa-moon';
            } else {
                currentTheme = 'dark';
                body.className = 'dark';
                themeIcon.className = 'fas fa-sun';
            }
        }

        // Fullscreen toggle
        function toggleFullscreen() {
            const fullscreenIcon = document.getElementById('fullscreen-icon');

            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().then(() => {
                    fullscreenIcon.className = 'fas fa-compress';
                }).catch(err => {
                    console.log('Error attempting to enable fullscreen:', err);
                });
            } else {
                document.exitFullscreen().then(() => {
                    fullscreenIcon.className = 'fas fa-expand';
                }).catch(err => {
                    console.log('Error attempting to exit fullscreen:', err);
                });
            }
        }

        // Listen for fullscreen changes
        document.addEventListener('fullscreenchange', () => {
            const fullscreenIcon = document.getElementById('fullscreen-icon');
            if (document.fullscreenElement) {
                fullscreenIcon.className = 'fas fa-compress';
            } else {
                fullscreenIcon.className = 'fas fa-expand';
            }
        });

        // Keyboard navigation
        function handleKeyPress(event) {
            switch (event.key) {
                case 'ArrowRight':
                case ' ':
                    event.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    event.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    event.preventDefault();
                    goToSlide(1);
                    break;
                case 'End':
                    event.preventDefault();
                    goToSlide(totalSlides);
                    break;
            }
        }

        // Initialize when page loads
        window.onload = init;
    </script>
</body>

</html>