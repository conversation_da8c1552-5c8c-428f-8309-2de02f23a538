// Slide data for the presentation
const slidesData = [
    {
        id: 1,
        title: "Title Slide",
        content: `
            <div class="slide-container">
                <!-- Floating background icons -->
                <div class="floating-icon icon-1"><i class="fas fa-cogs"></i></div>
                <div class="floating-icon icon-2"><i class="fas fa-lightbulb"></i></div>
                <div class="floating-icon icon-3"><i class="fas fa-users"></i></div>
                <div class="floating-icon icon-4"><i class="fas fa-rocket"></i></div>
                
                <!-- Energy pulse effect -->
                <div class="energy-pulse" style="top: 20%; left: 20%;"></div>
                <div class="energy-pulse" style="bottom: 20%; right: 20%; animation-delay: 2s;"></div>
                
                <div class="title-content">
                    <h1 class="main-title">Breaking the Feature Factory</h1>
                    <p class="subtitle">Build What Actually Matters</p>
                    
                    <div class="event-info">
                        <div class="event-text">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            Friends of Figma • Abu Dhabi • 2025
                        </div>
                    </div>
                    
                    <div class="speaker-info">
                        <div class="speaker-text">
                            <i class="fas fa-user mr-2"></i>
                            Puravi • Senior Product Leader • Veteran of Many Mistakes
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 2,
        title: "The Problem",
        content: `
            <div class="slide-container">
                <div class="content-grid">
                    <div>
                        <h1 class="problem-main-title">We're All Building the Wrong Things</h1>
                        <p class="problem-text">
                            Every day, product teams around the world ship features that nobody uses. 
                            We're busy, we're productive, we're hitting our deadlines... 
                            <span class="highlight">but are we actually helping anyone?</span>
                        </p>
                        
                        <div class="scenario-card">
                            <div class="scenario-title">
                                <i class="fas fa-exclamation-triangle mr-3"></i>
                                Sound Familiar?
                            </div>
                            <div class="scenario-text">
                                "We shipped 47 features last quarter! Our velocity is amazing!"<br><br>
                                <em>Meanwhile, users are still struggling with the same basic problems they had 6 months ago...</em>
                            </div>
                        </div>
                        
                        <div class="scenario-card">
                            <div class="scenario-title">
                                <i class="fas fa-clock mr-3"></i>
                                The Daily Reality
                            </div>
                            <div class="scenario-text">
                                Sprint planning feels like a feature wishlist. Stakeholders keep asking for "just one more thing." 
                                Your backlog is 200 items long, but you can't remember the last time a user said "wow, this changed my life."
                            </div>
                        </div>
                    </div>
                    
                    <div class="visual-section">
                        <div class="factory-visual">
                            <div class="factory-icon">
                                <i class="fas fa-industry"></i>
                            </div>
                            <div class="factory-title">The Feature Factory</div>
                            <div class="factory-subtitle">Optimized for output, not outcomes</div>
                        </div>
                        
                        <div class="stats-row">
                            <div class="stat-box">
                                <div class="stat-number">80%</div>
                                <div class="stat-label">Features rarely used</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number">3x</div>
                                <div class="stat-label">More features than needed</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 3,
        title: "Speaker Intro",
        content: `
            <div class="slide-container slide-3">
                <div class="content-grid">
                    <div>
                        <h1 class="main-title">Hi, I'm Puravi<br><span class="highlight">(And I've Made Every Mistake)</span></h1>
                        <p class="intro-text">
                            Before we dive into solutions, let me earn your trust by admitting I've been exactly where you are.
                            I've built features nobody wanted, ignored user feedback, and celebrated shipping over impact.
                        </p>

                        <div class="credentials-card">
                            <div class="credential-item">
                                <div class="credential-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="credential-text">8+ years building products at SenseHawk & Aldar</div>
                            </div>

                            <div class="credential-item">
                                <div class="credential-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="credential-text">Led teams from 5 to 50+ people</div>
                            </div>

                            <div class="credential-item">
                                <div class="credential-icon">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <div class="credential-text">Shipped 100+ features (some even useful!)</div>
                            </div>
                        </div>

                        <div class="mistakes-section">
                            <div class="mistakes-title">
                                <i class="fas fa-fire mr-3"></i>
                                My Greatest Hits (Misses)
                            </div>
                            <div class="mistake-item">Built a dashboard with 47 widgets because "more data = better"</div>
                            <div class="mistake-item">Spent 6 months on a feature 3 people used</div>
                            <div class="mistake-item">Ignored user research because "we know what they need"</div>
                            <div class="mistake-item">Celebrated velocity while users suffered in silence</div>
                            <div class="mistake-item">Said "just one more feature" approximately 1,000 times</div>
                        </div>
                    </div>

                    <div class="profile-section">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="profile-name">Puravi</div>
                        <div class="profile-title">Recovering Feature Factory Manager</div>

                        <div class="fun-fact">
                            <div class="fun-fact-text">
                                "I once built a feature so complex, even I couldn't figure out how to use it.
                                That's when I knew something had to change."
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 4,
        title: "Quick Poll",
        content: `
            <div class="slide-container">
                <!-- Floating emojis for engagement -->
                <div class="floating-emoji emoji-1">🤔</div>
                <div class="floating-emoji emoji-2">📊</div>
                <div class="floating-emoji emoji-3">🚀</div>
                <div class="floating-emoji emoji-4">💡</div>
                
                <h1 class="poll-main-title">Quick Reality Check</h1>
                
                <div class="poll-container">
                    <div class="poll-question">
                        How confident are you that your team is building the right things?
                    </div>
                    
                    <div class="poll-instruction">
                        Raise your hand for the option that best describes your current situation
                    </div>
                    
                    <div class="poll-options">
                        <div class="poll-option">
                            <div class="option-letter">A</div>
                            <div class="option-text">
                                Very confident - we validate everything with users and measure impact
                            </div>
                        </div>
                        
                        <div class="poll-option">
                            <div class="option-letter">B</div>
                            <div class="option-text">
                                Somewhat confident - we do research but still ship features that flop
                            </div>
                        </div>
                        
                        <div class="poll-option">
                            <div class="option-letter">C</div>
                            <div class="option-text">
                                Not very confident - we're mostly guessing and hoping for the best
                            </div>
                        </div>
                        
                        <div class="poll-option">
                            <div class="option-letter">D</div>
                            <div class="option-text">
                                What's confidence? We just build whatever stakeholders ask for
                            </div>
                        </div>
                    </div>
                    
                    <div class="engagement-note">
                        <div class="engagement-text">
                            Don't worry - if you picked C or D, you're in good company. 
                            Most product teams are flying blind, and that's exactly what we're here to fix!
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 5,
        title: "Factory Signs",
        content: `
            <div class="slide-container">
                <h1 class="main-title">The Feature Factory Trap</h1>

                <div class="warning-signs">
                    <div class="sign-card">
                        <div class="sign-icon">📊</div>
                        <div class="sign-title">Metrics Obsession</div>
                        <div class="sign-description">You celebrate shipping 50 features but can't name 3 user problems you solved</div>
                    </div>

                    <div class="sign-card">
                        <div class="sign-icon">🏃‍♂️</div>
                        <div class="sign-title">Always Rushing</div>
                        <div class="sign-description">"We don't have time for user research" is your team's unofficial motto</div>
                    </div>

                    <div class="sign-card">
                        <div class="sign-icon">📝</div>
                        <div class="sign-title">Endless Backlog</div>
                        <div class="sign-description">Your backlog has 200+ items and grows faster than you can ship</div>
                    </div>

                    <div class="sign-card">
                        <div class="sign-icon">🤷‍♀️</div>
                        <div class="sign-title">Feature Amnesia</div>
                        <div class="sign-description">You shipped something 3 months ago and nobody remembers why</div>
                    </div>

                    <div class="sign-card">
                        <div class="sign-icon">😤</div>
                        <div class="sign-title">Stakeholder Whiplash</div>
                        <div class="sign-description">Every executive has a "quick win" that becomes a 3-month project</div>
                    </div>

                    <div class="sign-card">
                        <div class="sign-icon">🎯</div>
                        <div class="sign-title">Success Theater</div>
                        <div class="sign-description">Your success metrics look great but users are still frustrated</div>
                    </div>
                </div>

                <div class="bottom-section">
                    <div class="bottom-title">
                        <span class="emoji">🚨</span>
                        If 3+ of these sound familiar, you're in a feature factory
                    </div>
                    <div class="bottom-text">
                        Don't worry - <span class="highlight">every successful product team</span> has been here.
                        The good news? There's a way out, and it's easier than you think.
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 6,
        title: "Real Cost",
        content: `
            <div class="slide-container">
                <div class="content-grid">
                    <div>
                        <h1 class="cost-main-title">The Hidden Price We Pay</h1>
                        <p class="cost-intro-text">
                            Feature factories aren't just inefficient - they're <span class="highlight">expensive</span>.
                            Here's what it really costs when we build the wrong things.
                        </p>

                        <div class="cost-examples">
                            <div class="cost-item">
                                <div class="cost-title">
                                    <span class="emoji">💸</span>
                                    Wasted Development Time
                                </div>
                                <div class="cost-description">
                                    6 months building a feature that 5% of users actually need
                                </div>
                            </div>

                            <div class="cost-item">
                                <div class="cost-title">
                                    <span class="emoji">😫</span>
                                    User Frustration
                                </div>
                                <div class="cost-description">
                                    Cluttered interfaces that make simple tasks harder than they should be
                                </div>
                            </div>

                            <div class="cost-item">
                                <div class="cost-title">
                                    <span class="emoji">🔧</span>
                                    Technical Debt
                                </div>
                                <div class="cost-description">
                                    Maintaining features nobody uses while real problems go unfixed
                                </div>
                            </div>

                            <div class="cost-item">
                                <div class="cost-title">
                                    <span class="emoji">🏃‍♀️</span>
                                    Team Burnout
                                </div>
                                <div class="cost-description">
                                    Talented people leaving because they can't see their impact
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="visual-section">
                        <div class="price-tag">
                            <div class="price-amount">$2.3M</div>
                            <div class="price-label">Annual cost per 100-person team</div>
                        </div>

                        <div class="impact-stats">
                            <div class="stat-box">
                                <div class="stat-number">80%</div>
                                <div class="stat-label">Features rarely used</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number">60%</div>
                                <div class="stat-label">Time on wrong priorities</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number">3x</div>
                                <div class="stat-label">Longer to find solutions</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number">40%</div>
                                <div class="stat-label">Higher team turnover</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 7,
        title: "Case Study",
        content: `
            <div class="slide-container">
                <h1 class="main-title">The Dashboard That Ate Everything</h1>

                <div class="story-container">
                    <div class="story-header">
                        <div class="story-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="story-title">A True Story from My Past</div>
                    </div>

                    <div class="story-content">
                        Our sales team asked for "better reporting." Simple enough, right?
                        Six months later, we had built a dashboard with <span class="highlight">47 different widgets</span>,
                        12 chart types, and enough filters to make a NASA engineer weep.
                    </div>

                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-step">1</div>
                            <div class="timeline-title">The Request</div>
                            <div class="timeline-text">"We need better sales reporting"</div>
                        </div>

                        <div class="timeline-item">
                            <div class="timeline-step">2</div>
                            <div class="timeline-title">The Assumption</div>
                            <div class="timeline-text">"More data = better decisions"</div>
                        </div>

                        <div class="timeline-item">
                            <div class="timeline-step">3</div>
                            <div class="timeline-title">The Reality</div>
                            <div class="timeline-text">"This is too complicated to use"</div>
                        </div>
                    </div>

                    <div class="story-content">
                        <strong>The plot twist?</strong> After talking to actual salespeople, we discovered they just wanted to know
                        <span class="highlight">which leads were getting cold</span>. A simple email notification solved their real problem
                        in 2 days, not 6 months.
                    </div>
                </div>

                <div class="lesson-learned">
                    <div class="lesson-title">
                        <span class="emoji">💡</span>
                        The Lesson
                    </div>
                    <div class="lesson-text">
                        We spent 6 months building what we <em>thought</em> they needed,
                        instead of 6 minutes asking what they <em>actually</em> needed.
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 8,
        title: "Why We Fall",
        content: `
            <div class="slide-container">
                <div class="content-grid">
                    <div>
                        <h1 class="why-fall-title">Why Smart People Build Dumb Things</h1>
                        <p class="why-fall-intro">
                            It's not about intelligence - it's about <span class="highlight">incentives, pressure, and human psychology</span>.
                            Here's why even brilliant teams fall into the feature factory trap.
                        </p>

                        <div class="reasons-list">
                            <div class="reason-item">
                                <div class="reason-title">
                                    <span class="emoji">⏰</span>
                                    Pressure to Ship
                                </div>
                                <div class="reason-description">
                                    "We need to show progress" becomes more important than showing impact
                                </div>
                            </div>

                            <div class="reason-item">
                                <div class="reason-title">
                                    <span class="emoji">🎯</span>
                                    Wrong Metrics
                                </div>
                                <div class="reason-description">
                                    When you measure features shipped, you get more features shipped
                                </div>
                            </div>

                            <div class="reason-item">
                                <div class="reason-title">
                                    <span class="emoji">🗣️</span>
                                    Loudest Voice Wins
                                </div>
                                <div class="reason-description">
                                    The executive who shouts loudest gets their pet feature built first
                                </div>
                            </div>

                            <div class="reason-item">
                                <div class="reason-title">
                                    <span class="emoji">🔮</span>
                                    Crystal Ball Syndrome
                                </div>
                                <div class="reason-description">
                                    "We know what users want" - famous last words of every failed product
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="visual-section">
                        <div class="brain-visual">
                            <div class="brain-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="brain-title">The Psychology</div>
                            <div class="brain-subtitle">Why our brains trick us into building wrong</div>
                        </div>

                        <div class="psychology-facts">
                            <div class="fact-box">
                                <div class="fact-title">Confirmation Bias</div>
                                <div class="fact-text">We look for evidence that supports our existing ideas</div>
                            </div>

                            <div class="fact-box">
                                <div class="fact-title">Sunk Cost Fallacy</div>
                                <div class="fact-text">"We've already built 80%, might as well finish"</div>
                            </div>

                            <div class="fact-box">
                                <div class="fact-title">Planning Fallacy</div>
                                <div class="fact-text">We consistently underestimate how long things take</div>
                            </div>

                            <div class="fact-box">
                                <div class="fact-title">Feature Creep</div>
                                <div class="fact-text">"While we're at it, let's add just one more thing..."</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 9,
        title: "Solution Framework",
        content: `
            <div class="slide-container">
                <h1 class="framework-title">The Value Engine Blueprint</h1>
                <p class="framework-subtitle">Three simple pillars to transform how you build products</p>

                <div class="pillars-container">
                    <div class="pillar-card">
                        <div class="pillar-number">1</div>
                        <div class="pillar-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="pillar-title">Ask Better Questions</div>
                        <div class="pillar-description">
                            Stop building what people ask for. Start solving what they actually need.
                        </div>
                    </div>

                    <div class="pillar-card">
                        <div class="pillar-number">2</div>
                        <div class="pillar-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <div class="pillar-title">Build Smart, Not Fast</div>
                        <div class="pillar-description">
                            Test your assumptions before you build. Measure what matters, not what's easy.
                        </div>
                    </div>

                    <div class="pillar-card">
                        <div class="pillar-number">3</div>
                        <div class="pillar-icon">
                            <i class="fas fa-sync"></i>
                        </div>
                        <div class="pillar-title">Never Stop Improving</div>
                        <div class="pillar-description">
                            Launch is just the beginning. Keep optimizing based on real user behavior.
                        </div>
                    </div>
                </div>

                <div class="transformation-arrow">
                    <div class="arrow-content">
                        <div class="arrow-text">From Feature Factory</div>
                        <div class="arrow-icon">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="arrow-text">To Value Engine</div>
                    </div>
                </div>

                <div class="bottom-message">
                    <div class="bottom-text">
                        <span class="highlight">The best part?</span> You don't need to transform everything overnight.
                        Start with one pillar, see the results, then expand.
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 10,
        title: "Pillar Discovery",
        content: `
            <div class="slide-container">
                <div class="content-grid">
                    <div>
                        <h1 class="main-title">Pillar 1: Ask Better Questions</h1>
                        <p class="intro-text">
                            The secret isn't building faster - it's building <span class="highlight">smarter</span>.
                            And that starts with asking the right questions before you write a single line of code.
                        </p>

                        <div class="magic-questions">
                            <div class="question-item">
                                <div class="question-title">
                                    <span class="emoji">🤔</span>
                                    The Magic Question #1
                                </div>
                                <div class="question-text">
                                    "What problem are we actually solving here?"
                                </div>
                            </div>

                            <div class="question-item">
                                <div class="question-title">
                                    <span class="emoji">👥</span>
                                    The Magic Question #2
                                </div>
                                <div class="question-text">
                                    "Who specifically has this problem, and how do we know?"
                                </div>
                            </div>

                            <div class="question-item">
                                <div class="question-title">
                                    <span class="emoji">📊</span>
                                    The Magic Question #3
                                </div>
                                <div class="question-text">
                                    "How will we know if we've actually solved it?"
                                </div>
                            </div>

                            <div class="question-item">
                                <div class="question-title">
                                    <span class="emoji">🚫</span>
                                    The Magic Question #4
                                </div>
                                <div class="question-text">
                                    "What happens if we don't build this at all?"
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="visual-section">
                        <div class="transformation-visual">
                            <div class="before-after">
                                <div class="before">
                                    <div class="before-title">❌ Old Way</div>
                                    <div class="before-text">"Build a dashboard with all the metrics"</div>
                                </div>

                                <div class="arrow">
                                    <i class="fas fa-arrow-right"></i>
                                </div>

                                <div class="after">
                                    <div class="after-title">✅ New Way</div>
                                    <div class="after-text">"Help managers spot problems before they become crises"</div>
                                </div>
                            </div>

                            <div class="before-after">
                                <div class="before">
                                    <div class="before-title">❌ Old Way</div>
                                    <div class="before-text">"Add advanced search filters"</div>
                                </div>

                                <div class="arrow">
                                    <i class="fas fa-arrow-right"></i>
                                </div>

                                <div class="after">
                                    <div class="after-title">✅ New Way</div>
                                    <div class="after-text">"Reduce time to find relevant info from 5 min to 30 sec"</div>
                                </div>
                            </div>

                            <div class="before-after">
                                <div class="before">
                                    <div class="before-title">❌ Old Way</div>
                                    <div class="before-text">"Build mobile app"</div>
                                </div>

                                <div class="arrow">
                                    <i class="fas fa-arrow-right"></i>
                                </div>

                                <div class="after">
                                    <div class="after-title">✅ New Way</div>
                                    <div class="after-text">"Let field workers update status without returning to office"</div>
                                </div>
                            </div>
                        </div>

                        <div class="framework-box">
                            <div class="framework-title">
                                <span class="emoji">💡</span>
                                The Simple Rule
                            </div>
                            <div class="framework-text">
                                If you can't explain the problem in one sentence,
                                you're not ready to build the solution.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 11,
        title: "Interactive Exercise",
        content: `
            <div class="slide-container">
                <div class="floating-timer">3:00</div>

                <h1 class="main-title">Let's Practice Together</h1>
                <p class="subtitle">Time to put those magic questions to work on a real scenario</p>

                <div class="exercise-container">
                    <div class="exercise-header">
                        <div class="exercise-title">
                            <span class="emoji">🎯</span>
                            Interactive Exercise
                        </div>
                        <div class="exercise-instruction">
                            Work with the person next to you to transform this feature request using our magic questions
                        </div>
                    </div>

                    <div class="scenario-box">
                        <div class="scenario-title">The Scenario</div>
                        <div class="scenario-text">
                            "Our sales team wants a new CRM integration that automatically syncs all customer data
                            and provides real-time analytics with customizable dashboards and advanced reporting capabilities."
                        </div>
                    </div>

                    <div class="questions-grid">
                        <div class="question-card">
                            <div class="question-number">1</div>
                            <div class="question-text">
                                What problem are we actually solving here?
                            </div>
                        </div>

                        <div class="question-card">
                            <div class="question-number">2</div>
                            <div class="question-text">
                                Who specifically has this problem, and how do we know?
                            </div>
                        </div>

                        <div class="question-card">
                            <div class="question-number">3</div>
                            <div class="question-text">
                                How will we know if we've actually solved it?
                            </div>
                        </div>

                        <div class="question-card">
                            <div class="question-number">4</div>
                            <div class="question-text">
                                What happens if we don't build this at all?
                            </div>
                        </div>
                    </div>
                </div>

                <div class="timer-section">
                    <div class="timer-title">
                        <span class="emoji">⏰</span>
                        You Have 3 Minutes
                    </div>
                    <div class="timer-text">
                        Discuss with your partner and see if you can uncover the <span class="highlight">real problem</span>
                        hiding behind this feature request. Ready... go!
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 12,
        title: "Pillar Development",
        content: `
            <div class="slide-container">
                <div class="content-grid">
                    <div>
                        <h1 class="main-title">Pillar 2: Build Smart, Not Fast</h1>
                        <p class="intro-text">
                            Speed kills... products. The fastest way to build the right thing is to
                            <span class="highlight">test your assumptions</span> before you commit to building.
                        </p>

                        <div class="principles-list">
                            <div class="principle-item">
                                <div class="principle-title">
                                    <span class="emoji">🧪</span>
                                    Start Small
                                </div>
                                <div class="principle-description">
                                    Build the smallest thing that can test your biggest assumption
                                </div>
                            </div>

                            <div class="principle-item">
                                <div class="principle-title">
                                    <span class="emoji">📊</span>
                                    Measure What Matters
                                </div>
                                <div class="principle-description">
                                    Track user behavior, not just feature usage
                                </div>
                            </div>

                            <div class="principle-item">
                                <div class="principle-title">
                                    <span class="emoji">🔄</span>
                                    Fail Fast, Learn Faster
                                </div>
                                <div class="principle-description">
                                    Every "failure" is just expensive user research
                                </div>
                            </div>

                            <div class="principle-item">
                                <div class="principle-title">
                                    <span class="emoji">🎯</span>
                                    One Thing at a Time
                                </div>
                                <div class="principle-description">
                                    Test one variable per experiment, or you'll never know what worked
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="visual-section">
                        <div class="cycle-visual">
                            <div class="cycle-title">Build-Measure-Learn</div>

                            <div class="cycle-steps">
                                <div class="cycle-step">
                                    <div class="step-icon build-icon">
                                        <i class="fas fa-hammer"></i>
                                    </div>
                                    <div class="step-title build-title">Build</div>
                                    <div class="step-description">Minimum viable test</div>
                                </div>

                                <div class="cycle-step">
                                    <div class="step-icon measure-icon">
                                        <i class="fas fa-chart-bar"></i>
                                    </div>
                                    <div class="step-title measure-title">Measure</div>
                                    <div class="step-description">Real user behavior</div>
                                </div>

                                <div class="cycle-step">
                                    <div class="step-icon learn-icon">
                                        <i class="fas fa-brain"></i>
                                    </div>
                                    <div class="step-title learn-title">Learn</div>
                                    <div class="step-description">What to do next</div>
                                </div>
                            </div>
                        </div>

                        <div class="example-box">
                            <div class="example-title">
                                <span class="emoji">💡</span>
                                Real Example
                            </div>
                            <div class="example-text">
                                Instead of building a full mobile app (6 months),
                                we sent SMS notifications (2 days).
                                Learned users didn't want another app -
                                they wanted faster updates!
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 13,
        title: "Pillar Optimization",
        content: `
            <div class="slide-container">
                <div class="content-grid">
                    <div>
                        <h1 class="main-title">Pillar 3: Never Stop Improving</h1>
                        <p class="intro-text">
                            Shipping is not the finish line - it's the <span class="highlight">starting line</span>.
                            The best products get better every week based on real user behavior.
                        </p>

                        <div class="optimization-mindset">
                            <div class="mindset-item">
                                <div class="mindset-title">
                                    <span class="emoji">🔍</span>
                                    Watch, Don't Assume
                                </div>
                                <div class="mindset-description">
                                    See how people actually use your product, not how you think they should
                                </div>
                            </div>

                            <div class="mindset-item">
                                <div class="mindset-title">
                                    <span class="emoji">🗑️</span>
                                    Kill Your Darlings
                                </div>
                                <div class="mindset-description">
                                    Remove features that don't deliver value, even if you love them
                                </div>
                            </div>

                            <div class="mindset-item">
                                <div class="mindset-title">
                                    <span class="emoji">🎯</span>
                                    Small Wins Add Up
                                </div>
                                <div class="mindset-description">
                                    1% improvements every week = 67% better by year end
                                </div>
                            </div>

                            <div class="mindset-item">
                                <div class="mindset-title">
                                    <span class="emoji">💬</span>
                                    Listen to Silence
                                </div>
                                <div class="mindset-description">
                                    The features nobody talks about are usually the ones nobody uses
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="visual-section">
                        <div class="optimization-visual">
                            <div class="visual-title">The Optimization Loop</div>

                            <div class="improvement-cycle">
                                <div class="cycle-step">
                                    <div class="step-number">1</div>
                                    <div class="step-text">Observe user behavior</div>
                                </div>

                                <div class="cycle-step">
                                    <div class="step-number">2</div>
                                    <div class="step-text">Identify friction points</div>
                                </div>

                                <div class="cycle-step">
                                    <div class="step-number">3</div>
                                    <div class="step-text">Test small improvements</div>
                                </div>

                                <div class="cycle-step">
                                    <div class="step-number">4</div>
                                    <div class="step-text">Measure impact</div>
                                </div>
                            </div>
                        </div>

                        <div class="success-story">
                            <div class="story-title">
                                <span class="emoji">🚀</span>
                                Success Story
                            </div>
                            <div class="story-text">
                                We noticed users clicking "Save" 3 times on average.
                                Added a simple loading spinner.
                                <span class="highlight">Reduced support tickets by 40%</span>
                                and improved user confidence.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 14,
        title: "Making It Stick",
        content: `
            <div class="slide-container">
                <h1 class="main-title">Making It Stick</h1>
                <p class="subtitle">Small changes that create lasting transformation</p>

                <div class="changes-grid">
                    <div class="change-category">
                        <div class="category-header">
                            <div class="category-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="category-title">Team Changes</div>
                        </div>

                        <div class="change-items">
                            <div class="change-item">
                                <div class="change-title">Start Every Meeting with "Why"</div>
                                <div class="change-description">Before discussing any feature, ask what problem it solves</div>
                            </div>

                            <div class="change-item">
                                <div class="change-title">User Story Fridays</div>
                                <div class="change-description">Spend 30 minutes each week reading actual user feedback</div>
                            </div>

                            <div class="change-item">
                                <div class="change-title">Outcome Champions</div>
                                <div class="change-description">Assign someone to track and report on user impact</div>
                            </div>

                            <div class="change-item">
                                <div class="change-title">Failure Celebrations</div>
                                <div class="change-description">Share what you learned when experiments don't work</div>
                            </div>
                        </div>
                    </div>

                    <div class="change-category">
                        <div class="category-header">
                            <div class="category-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="category-title">Process Changes</div>
                        </div>

                        <div class="change-items">
                            <div class="change-item">
                                <div class="change-title">Problem-First Backlogs</div>
                                <div class="change-description">Write user problems, not feature requests</div>
                            </div>

                            <div class="change-item">
                                <div class="change-title">Success Criteria Required</div>
                                <div class="change-description">No work starts without clear success metrics</div>
                            </div>

                            <div class="change-item">
                                <div class="change-title">Monthly Feature Audits</div>
                                <div class="change-description">Review what you shipped and its actual impact</div>
                            </div>

                            <div class="change-item">
                                <div class="change-title">User Research Budget</div>
                                <div class="change-description">Allocate 10% of development time to talking to users</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-section">
                    <div class="bottom-title">
                        <span class="emoji">🎯</span>
                        The Secret Sauce
                    </div>
                    <div class="bottom-text">
                        You don't need to change everything at once. Pick <span class="highlight">one thing</span>
                        from this list and try it for 30 days. Small changes compound into big transformations.
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 15,
        title: "Your Plan",
        content: `
            <div class="slide-container">
                <h1 class="main-title">Your 30-Day Challenge</h1>
                <p class="subtitle">Start your transformation journey with these simple, actionable steps</p>

                <div class="challenge-container">
                    <div class="challenge-header">
                        <div class="challenge-title">
                            <span class="emoji">🚀</span>
                            The Feature Factory Recovery Program
                        </div>
                        <div class="challenge-description">
                            Four weeks to break the feature factory habit and start building what matters
                        </div>
                    </div>

                    <div class="weeks-grid">
                        <div class="week-card">
                            <div class="week-number">1</div>
                            <div class="week-title">Audit & Assess</div>
                            <div class="week-action">
                                List your last 10 features. Ask: "What problem did this solve?"
                                If you can't answer, you found your first clue.
                            </div>
                        </div>

                        <div class="week-card">
                            <div class="week-number">2</div>
                            <div class="week-title">Ask Better Questions</div>
                            <div class="week-action">
                                Before building anything new, use the 4 magic questions.
                                Practice on one real feature request.
                            </div>
                        </div>

                        <div class="week-card">
                            <div class="week-number">3</div>
                            <div class="week-title">Test Small</div>
                            <div class="week-action">
                                Pick one assumption and test it without building.
                                Use surveys, interviews, or simple prototypes.
                            </div>
                        </div>

                        <div class="week-card">
                            <div class="week-number">4</div>
                            <div class="week-title">Measure & Learn</div>
                            <div class="week-action">
                                Set up one simple metric that tracks user success, not feature usage.
                                Share results with your team.
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bottom-section">
                    <div class="motivation-box">
                        <div class="motivation-title">
                            <span class="emoji">💪</span>
                            Why This Works
                        </div>
                        <div class="motivation-text">
                            Small changes create <span class="highlight">big momentum</span>.
                            In 30 days, you'll have real evidence that this approach works,
                            making it easier to convince your team and stakeholders.
                        </div>
                    </div>

                    <div class="support-box">
                        <div class="support-title">
                            <span class="emoji">🤝</span>
                            You're Not Alone
                        </div>
                        <div class="support-text">
                            Thousands of product teams have made this transition.
                            Start with <span class="highlight">one small step</span> and build from there.
                            Progress beats perfection.
                        </div>
                    </div>
                </div>
            </div>
        `
    },
    {
        id: 16,
        title: "Let's Connect",
        content: `
            <div class="slide-container">
                <!-- Floating background elements -->
                <div class="floating-elements">
                    <div class="floating-icon icon-1"><i class="fas fa-lightbulb"></i></div>
                    <div class="floating-icon icon-2"><i class="fas fa-users"></i></div>
                    <div class="floating-icon icon-3"><i class="fas fa-rocket"></i></div>
                    <div class="floating-icon icon-4"><i class="fas fa-heart"></i></div>
                </div>

                <div class="content-wrapper">
                    <h1 class="main-title">Let's Keep Building Better</h1>
                    <p class="subtitle">The conversation doesn't end here</p>

                    <div class="connect-grid">
                        <div class="connect-card">
                            <div class="connect-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="connect-title">Get Resources</div>
                            <div class="connect-description">
                                Email me for templates, frameworks, and tools to start your transformation
                            </div>
                        </div>

                        <div class="connect-card">
                            <div class="connect-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <div class="connect-title">Share Your Story</div>
                            <div class="connect-description">
                                Tell me about your feature factory experiences - the good, bad, and ugly
                            </div>
                        </div>

                        <div class="connect-card">
                            <div class="connect-icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                            <div class="connect-title">Let's Collaborate</div>
                            <div class="connect-description">
                                Working on similar challenges? Let's learn from each other
                            </div>
                        </div>
                    </div>

                    <div class="final-message">
                        <div class="final-title">
                            <span class="emoji">🌟</span>
                            Remember
                        </div>
                        <div class="final-text">
                            Every great product started with someone asking <span class="highlight">"What if we built something people actually need?"</span>
                            You have the power to break the feature factory cycle and create products that truly matter.
                        </div>

                        <div class="contact-info">
                            <div class="contact-text">
                                <i class="fas fa-envelope mr-2"></i>
                                <EMAIL> • LinkedIn: /in/puravi
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    }
];

// Function to generate slides
function generateSlides() {
    const slidesContainer = document.getElementById('slides-container');
    slidesContainer.innerHTML = '';

    slidesData.forEach((slideData, index) => {
        const slideElement = document.createElement('div');
        slideElement.className = `slide ${index === 0 ? 'active' : ''}`;
        slideElement.id = `slide-${slideData.id}`;
        slideElement.innerHTML = slideData.content;
        slidesContainer.appendChild(slideElement);
    });
}
