<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Breaking the Feature Factory - Slideshow</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', sans-serif;
            overflow: hidden;
            transition: background-color 0.3s ease;
        }

        .slideshow-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 80px 20px 120px 20px;
            box-sizing: border-box;
        }

        .slide-wrapper {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .slide {
            display: none;
            width: 100%;
            height: 100%;
            max-width: 1280px;
            max-height: 100%;
            overflow: hidden;
            position: relative;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .slide-container {
            width: 100%;
            max-width: 1280px;
            min-height: 720px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            font-family: 'Inter', sans-serif;
            padding: 60px;
            box-sizing: border-box;
            transform-origin: center center;
        }

        /* Top Controls */
        .top-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Navigation Bar */
        .nav-container {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
        }

        .nav-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 15px 25px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 10px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-size: 16px;
            min-width: 45px;
            text-align: center;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .slide-indicator {
            color: white;
            font-weight: 500;
            font-size: 14px;
            padding: 0 15px;
        }

        /* Dark theme background */
        body.dark {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        }

        body.dark .slide-container {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        }

        /* Light theme background */
        body.light {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        body.light .slide-container {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        body.light .control-btn,
        body.light .nav-bar,
        body.light .nav-btn {
            background: rgba(0, 0, 0, 0.1);
            border-color: rgba(0, 0, 0, 0.2);
            color: #1e293b;
        }

        body.light .control-btn:hover,
        body.light .nav-btn:hover {
            background: rgba(0, 0, 0, 0.2);
        }

        body.light .slide-indicator {
            color: #1e293b;
        }

        /* Responsive scaling */
        @media (max-width: 1400px) {
            .slide-container {
                transform: scale(0.9);
            }
        }

        @media (max-width: 1200px) {
            .slide-container {
                transform: scale(0.8);
            }
        }

        @media (max-width: 1000px) {
            .slide-container {
                transform: scale(0.7);
            }
        }

        @media (max-width: 800px) {
            .slide-container {
                transform: scale(0.6);
                padding: 40px;
            }

            .nav-bar {
                padding: 12px 20px;
                gap: 15px;
            }

            .nav-btn {
                padding: 8px 12px;
                font-size: 14px;
                min-width: 40px;
            }

            .control-btn {
                width: 45px;
                height: 45px;
                font-size: 16px;
            }

            .top-controls {
                gap: 8px;
            }
        }

        /* Slide 1 - Title Slide Styles */
        .title-content {
            text-align: center;
            z-index: 2;
        }

        .main-title {
            font-size: 64px;
            font-weight: 800;
            color: #ffffff;
            margin-bottom: 24px;
            line-height: 1.1;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 28px;
            color: #f97316;
            font-weight: 600;
            margin-bottom: 40px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .event-info {
            background: rgba(59, 130, 246, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 16px;
            padding: 24px 40px;
            margin-bottom: 32px;
        }

        .event-text {
            font-size: 18px;
            color: #3b82f6;
            font-weight: 500;
        }

        .speaker-info {
            background: rgba(249, 115, 22, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(249, 115, 22, 0.3);
            border-radius: 16px;
            padding: 20px 32px;
        }

        .speaker-text {
            font-size: 16px;
            color: #f97316;
            font-weight: 500;
        }

        .floating-icon {
            position: absolute;
            color: rgba(59, 130, 246, 0.2);
            animation: float 6s ease-in-out infinite;
        }

        .icon-1 {
            top: 10%;
            left: 10%;
            font-size: 40px;
            animation-delay: 0s;
        }

        .icon-2 {
            top: 20%;
            right: 15%;
            font-size: 32px;
            animation-delay: 2s;
        }

        .icon-3 {
            bottom: 15%;
            left: 15%;
            font-size: 36px;
            animation-delay: 4s;
        }

        .icon-4 {
            bottom: 25%;
            right: 10%;
            font-size: 28px;
            animation-delay: 1s;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-20px);
            }
        }

        .energy-pulse {
            position: absolute;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
            z-index: 0;
        }

        @keyframes pulse {

            0%,
            100% {
                transform: scale(1);
                opacity: 0.3;
            }

            50% {
                transform: scale(1.2);
                opacity: 0.1;
            }
        }

        /* Light theme adjustments */
        body.light .main-title {
            color: #1e293b;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        body.light .subtitle {
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        body.light .floating-icon {
            color: rgba(59, 130, 246, 0.3);
        }

        body.light .energy-pulse {
            background: radial-gradient(circle, rgba(59, 130, 246, 0.15) 0%, transparent 70%);
        }

        /* Slide 4 - Quick Poll Styles */
        .poll-main-title {
            font-size: 56px;
            font-weight: 800;
            color: #ffffff;
            text-align: center;
            margin-bottom: 48px;
            line-height: 1.1;
        }

        .poll-container {
            background: rgba(59, 130, 246, 0.1);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 24px;
            padding: 48px;
            width: 100%;
            max-width: 900px;
            text-align: center;
        }

        .poll-question {
            font-size: 28px;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 32px;
            line-height: 1.3;
        }

        .poll-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }

        .poll-option {
            background: rgba(249, 115, 22, 0.1);
            border: 2px solid rgba(249, 115, 22, 0.3);
            border-radius: 16px;
            padding: 32px 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .poll-option:hover {
            background: rgba(249, 115, 22, 0.2);
            border-color: rgba(249, 115, 22, 0.5);
            transform: translateY(-4px);
        }

        .option-letter {
            background: #f97316;
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 20px;
            font-weight: 700;
        }

        .option-text {
            font-size: 18px;
            color: #f1f5f9;
            font-weight: 600;
            line-height: 1.4;
        }

        .poll-instruction {
            font-size: 20px;
            color: #cbd5e1;
            margin-bottom: 24px;
            font-weight: 500;
        }

        .engagement-note {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin-top: 32px;
        }

        .engagement-text {
            font-size: 16px;
            color: #10b981;
            text-align: center;
            font-style: italic;
        }

        .floating-emoji {
            position: absolute;
            font-size: 32px;
            animation: float 4s ease-in-out infinite;
        }

        .emoji-1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .emoji-2 {
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }

        .emoji-3 {
            bottom: 20%;
            left: 15%;
            animation-delay: 1s;
        }

        .emoji-4 {
            bottom: 15%;
            right: 15%;
            animation-delay: 3s;
        }

        /* Light theme adjustments for poll */
        body.light .poll-main-title {
            color: #1e293b;
        }

        body.light .option-text {
            color: #1e293b;
        }

        body.light .poll-instruction {
            color: #64748b;
        }

        /* Slide 2 - The Problem Styles */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            width: 100%;
            align-items: center;
        }

        .problem-main-title {
            font-size: 48px;
            font-weight: 800;
            color: #ffffff;
            margin-bottom: 32px;
            line-height: 1.1;
        }

        .problem-text {
            font-size: 20px;
            color: #cbd5e1;
            line-height: 1.6;
            margin-bottom: 32px;
        }

        .scenario-card {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .scenario-title {
            font-size: 18px;
            font-weight: 700;
            color: #ef4444;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .scenario-text {
            font-size: 16px;
            color: #f1f5f9;
            line-height: 1.5;
        }

        .visual-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .factory-visual {
            background: rgba(59, 130, 246, 0.1);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-bottom: 32px;
            width: 100%;
        }

        .factory-icon {
            font-size: 80px;
            color: #3b82f6;
            margin-bottom: 20px;
            animation: spin 8s linear infinite;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        .factory-title {
            font-size: 24px;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 12px;
        }

        .factory-subtitle {
            font-size: 16px;
            color: #94a3b8;
        }

        .stats-row {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }

        .stat-box {
            background: rgba(249, 115, 22, 0.1);
            border: 1px solid rgba(249, 115, 22, 0.3);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            flex: 1;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 800;
            color: #f97316;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #cbd5e1;
            font-weight: 500;
        }

        .highlight {
            color: #f97316;
            font-weight: 700;
        }

        /* Light theme adjustments for slide 2 */
        body.light .problem-main-title {
            color: #1e293b;
        }

        body.light .problem-text {
            color: #64748b;
        }

        body.light .scenario-text {
            color: #1e293b;
        }

        body.light .stat-label {
            color: #64748b;
        }

        /* Slide 3 - Speaker Intro Styles */
        .intro-content {
            text-align: center;
            width: 100%;
            max-width: 1000px;
        }

        .intro-main-title {
            font-size: 64px;
            font-weight: 800;
            color: #ffffff;
            margin-bottom: 16px;
            line-height: 1.1;
        }

        .intro-subtitle {
            font-size: 24px;
            color: #f97316;
            font-weight: 600;
            margin-bottom: 48px;
        }

        .intro-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 32px;
            margin-bottom: 48px;
        }

        .intro-card {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 16px;
            padding: 32px 24px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .intro-card:hover {
            background: rgba(59, 130, 246, 0.2);
            transform: translateY(-4px);
        }

        .intro-icon {
            font-size: 48px;
            color: #3b82f6;
            margin-bottom: 20px;
        }

        .intro-text h3 {
            font-size: 20px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .intro-text p {
            font-size: 16px;
            color: #cbd5e1;
            line-height: 1.4;
        }

        .intro-quote {
            background: rgba(249, 115, 22, 0.1);
            border: 1px solid rgba(249, 115, 22, 0.3);
            border-radius: 16px;
            padding: 32px;
            font-style: italic;
        }

        .intro-quote p {
            font-size: 20px;
            color: #f97316;
            line-height: 1.6;
            margin: 0;
        }

        /* Light theme adjustments for slide 3 */
        body.light .intro-main-title {
            color: #1e293b;
        }

        body.light .intro-text h3 {
            color: #1e293b;
        }

        body.light .intro-text p {
            color: #64748b;
        }
    </style>
</head>

<body class="dark">
    <!-- Top Controls -->
    <div class="top-controls">
        <div class="control-btn" onclick="toggleFullscreen()" title="Toggle Fullscreen">
            <i class="fas fa-expand" id="fullscreen-icon"></i>
        </div>
        <div class="control-btn" onclick="toggleTheme()" title="Toggle Theme">
            <i class="fas fa-sun" id="theme-icon"></i>
        </div>
    </div>

    <!-- Slideshow Container -->
    <div class="slideshow-container">
        <div class="slide-wrapper">
            <!-- Slides will be added here -->
            <div id="slides-container"></div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="nav-container">
        <div class="nav-bar">
            <button class="nav-btn" onclick="previousSlide()" id="prev-btn">
                <i class="fas fa-chevron-left"></i>
            </button>

            <div class="slide-indicator">
                <span id="current-slide">1</span> / <span id="total-slides">16</span>
            </div>

            <button class="nav-btn" onclick="nextSlide()" id="next-btn">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>

    <script>
        let currentSlide = 1;
        const totalSlides = 4; // Update as we add more slides
        let currentTheme = 'dark';

        // Slide data structure
        const slides = [];

        // Initialize slideshow
        function init() {
            createSlides();
            showSlide(currentSlide);
            updateNavigation();

            // Keyboard navigation
            document.addEventListener('keydown', handleKeyPress);

            // Handle window resize
            window.addEventListener('resize', handleResize);
        }

        // Handle window resize
        function handleResize() {
            // Slides will auto-scale with CSS media queries
        }

        // Create all slides
        function createSlides() {
            const slidesContainer = document.getElementById('slides-container');

            // Slide 1: Title Slide
            slidesContainer.innerHTML += `
                <div class="slide active" id="slide-1">
                    <div class="slide-container">
                        <!-- Floating background icons -->
                        <div class="floating-icon icon-1"><i class="fas fa-cogs"></i></div>
                        <div class="floating-icon icon-2"><i class="fas fa-lightbulb"></i></div>
                        <div class="floating-icon icon-3"><i class="fas fa-users"></i></div>
                        <div class="floating-icon icon-4"><i class="fas fa-rocket"></i></div>

                        <!-- Energy pulse effect -->
                        <div class="energy-pulse" style="top: 20%; left: 20%;"></div>
                        <div class="energy-pulse" style="bottom: 20%; right: 20%; animation-delay: 2s;"></div>

                        <div class="title-content">
                            <h1 class="main-title">Breaking the Feature Factory</h1>
                            <p class="subtitle">Build What Actually Matters</p>

                            <div class="event-info">
                                <div class="event-text">
                                    <i class="fas fa-map-marker-alt mr-2"></i>
                                    Friends of Figma • Abu Dhabi • 2025
                                </div>
                            </div>

                            <div class="speaker-info">
                                <div class="speaker-text">
                                    <i class="fas fa-user mr-2"></i>
                                    Puravi • Senior Product Leader • Veteran of Many Mistakes
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Slide 2: The Problem
            slidesContainer.innerHTML += `
                <div class="slide" id="slide-2">
                    <div class="slide-container">
                        <div class="content-grid">
                            <div>
                                <h1 class="problem-main-title">We're All Building the Wrong Things</h1>
                                <p class="problem-text">
                                    Every day, product teams around the world ship features that nobody uses.
                                    We're busy, we're productive, we're hitting our deadlines...
                                    <span class="highlight">but are we actually helping anyone?</span>
                                </p>

                                <div class="scenario-card">
                                    <div class="scenario-title">
                                        <i class="fas fa-exclamation-triangle mr-3"></i>
                                        Sound Familiar?
                                    </div>
                                    <div class="scenario-text">
                                        "We shipped 47 features last quarter! Our velocity is amazing!"<br><br>
                                        <em>Meanwhile, users are still struggling with the same basic problems they had 6 months ago...</em>
                                    </div>
                                </div>

                                <div class="scenario-card">
                                    <div class="scenario-title">
                                        <i class="fas fa-clock mr-3"></i>
                                        The Daily Reality
                                    </div>
                                    <div class="scenario-text">
                                        Sprint planning feels like a feature wishlist. Stakeholders keep asking for "just one more thing."
                                        Your backlog is 200 items long, but you can't remember the last time a user said "wow, this changed my life."
                                    </div>
                                </div>
                            </div>

                            <div class="visual-section">
                                <div class="factory-visual">
                                    <div class="factory-icon">
                                        <i class="fas fa-industry"></i>
                                    </div>
                                    <div class="factory-title">The Feature Factory</div>
                                    <div class="factory-subtitle">Optimized for output, not outcomes</div>
                                </div>

                                <div class="stats-row">
                                    <div class="stat-box">
                                        <div class="stat-number">80%</div>
                                        <div class="stat-label">Features rarely used</div>
                                    </div>
                                    <div class="stat-box">
                                        <div class="stat-number">3x</div>
                                        <div class="stat-label">More features than needed</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Slide 3: Speaker Intro
            slidesContainer.innerHTML += `
                <div class="slide" id="slide-3">
                    <div class="slide-container">
                        <div class="intro-content">
                            <h1 class="intro-main-title">Hi, I'm Puravi</h1>
                            <p class="intro-subtitle">Senior Product Leader & Recovering Feature Factory Addict</p>

                            <div class="intro-grid">
                                <div class="intro-card">
                                    <div class="intro-icon">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <div class="intro-text">
                                        <h3>10+ Years</h3>
                                        <p>Building products at startups and enterprises</p>
                                    </div>
                                </div>

                                <div class="intro-card">
                                    <div class="intro-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="intro-text">
                                        <h3>Countless Mistakes</h3>
                                        <p>Shipped features nobody wanted or used</p>
                                    </div>
                                </div>

                                <div class="intro-card">
                                    <div class="intro-icon">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <div class="intro-text">
                                        <h3>Hard-Won Lessons</h3>
                                        <p>Learned what actually drives user value</p>
                                    </div>
                                </div>
                            </div>

                            <div class="intro-quote">
                                <p>"I've been where you are. I've felt the pressure to ship, ship, ship.
                                And I've learned the hard way that being busy doesn't mean being effective."</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Slide 4: Quick Poll
            slidesContainer.innerHTML += `
                <div class="slide" id="slide-4">
                    <div class="slide-container">
                        <!-- Floating emojis for engagement -->
                        <div class="floating-emoji emoji-1">🤔</div>
                        <div class="floating-emoji emoji-2">📊</div>
                        <div class="floating-emoji emoji-3">🚀</div>
                        <div class="floating-emoji emoji-4">💡</div>

                        <h1 class="poll-main-title">Quick Reality Check</h1>

                        <div class="poll-container">
                            <div class="poll-question">
                                How confident are you that your team is building the right things?
                            </div>

                            <div class="poll-instruction">
                                Raise your hand for the option that best describes your current situation
                            </div>

                            <div class="poll-options">
                                <div class="poll-option">
                                    <div class="option-letter">A</div>
                                    <div class="option-text">
                                        Very confident - we validate everything with users and measure impact
                                    </div>
                                </div>

                                <div class="poll-option">
                                    <div class="option-letter">B</div>
                                    <div class="option-text">
                                        Somewhat confident - we do research but still ship features that flop
                                    </div>
                                </div>

                                <div class="poll-option">
                                    <div class="option-letter">C</div>
                                    <div class="option-text">
                                        Not very confident - we're mostly guessing and hoping for the best
                                    </div>
                                </div>

                                <div class="poll-option">
                                    <div class="option-letter">D</div>
                                    <div class="option-text">
                                        What's confidence? We just build whatever stakeholders ask for
                                    </div>
                                </div>
                            </div>

                            <div class="engagement-note">
                                <div class="engagement-text">
                                    Don't worry - if you picked C or D, you're in good company.
                                    Most product teams are flying blind, and that's exactly what we're here to fix!
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            console.log('Slides created');
        }

        // Show specific slide
        function showSlide(slideNumber) {
            // Hide all slides
            const allSlides = document.querySelectorAll('.slide');
            allSlides.forEach(slide => slide.classList.remove('active'));

            // Show current slide
            const currentSlideElement = document.getElementById(`slide-${slideNumber}`);
            if (currentSlideElement) {
                currentSlideElement.classList.add('active');
            }

            updateNavigation();
        }

        // Navigation functions
        function nextSlide() {
            if (currentSlide < totalSlides) {
                currentSlide++;
                showSlide(currentSlide);
            }
        }

        function previousSlide() {
            if (currentSlide > 1) {
                currentSlide--;
                showSlide(currentSlide);
            }
        }

        function goToSlide(slideNumber) {
            currentSlide = slideNumber;
            showSlide(currentSlide);
        }

        // Update navigation state
        function updateNavigation() {
            document.getElementById('current-slide').textContent = currentSlide;
            document.getElementById('total-slides').textContent = totalSlides;

            // Update buttons
            document.getElementById('prev-btn').disabled = currentSlide === 1;
            document.getElementById('next-btn').disabled = currentSlide === totalSlides;
        }

        // Theme toggle
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');

            if (currentTheme === 'dark') {
                currentTheme = 'light';
                body.className = 'light';
                themeIcon.className = 'fas fa-moon';
            } else {
                currentTheme = 'dark';
                body.className = 'dark';
                themeIcon.className = 'fas fa-sun';
            }
        }

        // Fullscreen toggle
        function toggleFullscreen() {
            const fullscreenIcon = document.getElementById('fullscreen-icon');

            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().then(() => {
                    fullscreenIcon.className = 'fas fa-compress';
                }).catch(err => {
                    console.log('Error attempting to enable fullscreen:', err);
                });
            } else {
                document.exitFullscreen().then(() => {
                    fullscreenIcon.className = 'fas fa-expand';
                }).catch(err => {
                    console.log('Error attempting to exit fullscreen:', err);
                });
            }
        }

        // Listen for fullscreen changes
        document.addEventListener('fullscreenchange', () => {
            const fullscreenIcon = document.getElementById('fullscreen-icon');
            if (document.fullscreenElement) {
                fullscreenIcon.className = 'fas fa-compress';
            } else {
                fullscreenIcon.className = 'fas fa-expand';
            }
        });

        // Keyboard navigation
        function handleKeyPress(event) {
            switch (event.key) {
                case 'ArrowRight':
                case ' ':
                    event.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    event.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    event.preventDefault();
                    goToSlide(1);
                    break;
                case 'End':
                    event.preventDefault();
                    goToSlide(totalSlides);
                    break;
            }
        }

        // Initialize when page loads
        window.onload = init;
    </script>
</body>

</html>