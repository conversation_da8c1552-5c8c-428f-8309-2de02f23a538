<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Breaking the Feature Factory - Slideshow</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', sans-serif;
            overflow: hidden;
            transition: background-color 0.3s ease;
        }

        .slideshow-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px 20px 120px 20px;
            /* Extra bottom padding for navigation */
            box-sizing: border-box;
        }

        .slide-wrapper {
            width: 100%;
            height: 100%;
            max-width: calc(100vh * 1.778);
            /* 16:9 aspect ratio */
            max-height: calc(100vw * 0.5625);
            /* 16:9 aspect ratio */
            position: relative;
        }

        .slide-frame {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            transition: opacity 0.5s ease-in-out;
            transform: scale(1);
            transform-origin: center center;
        }

        /* Theme Toggle */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50px;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-weight: 500;
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Navigation Bar */
        .nav-container {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
        }

        .nav-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 15px 25px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 10px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-size: 16px;
            min-width: 45px;
            text-align: center;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .slide-indicator {
            color: white;
            font-weight: 500;
            font-size: 14px;
            padding: 0 15px;
        }



        /* Dark theme background */
        body.dark {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        }

        /* Light theme background */
        body.light {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        body.light .theme-toggle,
        body.light .nav-bar,
        body.light .nav-btn {
            background: rgba(0, 0, 0, 0.1);
            border-color: rgba(0, 0, 0, 0.2);
            color: #1e293b;
        }

        body.light .theme-toggle:hover,
        body.light .nav-btn:hover {
            background: rgba(0, 0, 0, 0.2);
        }

        body.light .slide-indicator {
            color: #1e293b;
        }



        /* Loading animation */
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
        }

        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Responsive design */
        @media (max-width: 1400px) {
            .slideshow-container {
                padding: 20px;
            }
        }

        @media (max-width: 768px) {
            .nav-bar {
                padding: 12px 20px;
                gap: 15px;
            }

            .nav-btn {
                padding: 8px 12px;
                font-size: 14px;
                min-width: 40px;
            }

            .theme-toggle {
                padding: 10px 16px;
                font-size: 14px;
            }
        }
    </style>
</head>

<body class="dark">
    <!-- Theme Toggle -->
    <div class="theme-toggle" onclick="toggleTheme()">
        <i class="fas fa-sun" id="theme-icon"></i>
        <span id="theme-text">Light</span>
    </div>

    <!-- Slideshow Container -->
    <div class="slideshow-container">
        <div class="slide-wrapper">
            <div class="loading" id="loading">
                <div class="spinner"></div>
                Loading presentation...
            </div>
            <iframe id="slide-frame" class="slide-frame" style="display: none;" scrolling="no"></iframe>
        </div>
    </div>

    <!-- Navigation -->
    <div class="nav-container">
        <div class="nav-bar">
            <button class="nav-btn" onclick="previousSlide()" id="prev-btn">
                <i class="fas fa-chevron-left"></i>
            </button>



            <div class="slide-indicator">
                <span id="current-slide">1</span> / <span id="total-slides">16</span>
            </div>

            <button class="nav-btn" onclick="nextSlide()" id="next-btn">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>

    <script>
        let currentSlide = 1;
        const totalSlides = 16;
        let currentTheme = 'dark';

        // Initialize slideshow
        function init() {
            loadSlide(currentSlide);
            updateNavigation();

            // Handle window resize
            window.addEventListener('resize', handleResize);

            // Keyboard navigation
            document.addEventListener('keydown', handleKeyPress);
        }

        // Handle window resize
        function handleResize() {
            const iframe = document.getElementById('slide-frame');
            if (iframe.src) {
                resizeSlideContent();
            }
        }

        // Resize slide content to fit without scrollbars
        function resizeSlideContent() {
            const iframe = document.getElementById('slide-frame');

            iframe.onload = () => {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const slideContainer = iframeDoc.querySelector('.slide-container');

                    if (slideContainer) {
                        // Get the wrapper dimensions
                        const wrapper = iframe.parentElement;
                        const wrapperRect = wrapper.getBoundingClientRect();

                        // Calculate scale to fit content
                        const originalWidth = 1280;
                        const originalHeight = 720;

                        const scaleX = wrapperRect.width / originalWidth;
                        const scaleY = wrapperRect.height / originalHeight;
                        const scale = Math.min(scaleX, scaleY);

                        // Apply scaling to the slide container
                        slideContainer.style.transform = `scale(${scale})`;
                        slideContainer.style.transformOrigin = 'center center';

                        // Adjust iframe body to prevent scrollbars
                        iframeDoc.body.style.margin = '0';
                        iframeDoc.body.style.padding = '0';
                        iframeDoc.body.style.overflow = 'hidden';
                        iframeDoc.body.style.display = 'flex';
                        iframeDoc.body.style.justifyContent = 'center';
                        iframeDoc.body.style.alignItems = 'center';
                        iframeDoc.body.style.minHeight = '100vh';
                    }
                } catch (e) {
                    // Cross-origin restrictions might prevent access
                    console.log('Cannot access iframe content for resizing');
                }
            };
        }



        // Load specific slide
        function loadSlide(slideNumber) {
            const iframe = document.getElementById('slide-frame');
            const loading = document.getElementById('loading');
            const slideFile = String(slideNumber).padStart(2, '0');

            // Determine slide filename based on slide number
            const slideNames = [
                'title_slide', 'the_problem', 'speaker_intro', 'quick_poll',
                'factory_signs', 'real_cost', 'case_study', 'why_we_fall',
                'solution_framework', 'pillar_discovery', 'interactive_exercise',
                'pillar_development', 'pillar_optimization', 'making_it_stick',
                'your_plan', 'lets_connect'
            ];

            const slideName = slideNames[slideNumber - 1];
            const slidePath = `${currentTheme}_theme/${slideFile}_${slideName}.html`;

            loading.style.display = 'block';
            iframe.style.display = 'none';

            iframe.onload = () => {
                loading.style.display = 'none';
                iframe.style.display = 'block';
                resizeSlideContent();
            };

            iframe.src = slidePath;
            updateNavigation();
        }

        // Navigation functions
        function nextSlide() {
            if (currentSlide < totalSlides) {
                currentSlide++;
                loadSlide(currentSlide);
            }
        }

        function previousSlide() {
            if (currentSlide > 1) {
                currentSlide--;
                loadSlide(currentSlide);
            }
        }

        function goToSlide(slideNumber) {
            currentSlide = slideNumber;
            loadSlide(currentSlide);
        }

        // Update navigation state
        function updateNavigation() {
            document.getElementById('current-slide').textContent = currentSlide;
            document.getElementById('total-slides').textContent = totalSlides;

            // Update buttons
            document.getElementById('prev-btn').disabled = currentSlide === 1;
            document.getElementById('next-btn').disabled = currentSlide === totalSlides;
        }

        // Theme toggle
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');

            if (currentTheme === 'dark') {
                currentTheme = 'light';
                body.className = 'light';
                themeIcon.className = 'fas fa-moon';
                themeText.textContent = 'Dark';
            } else {
                currentTheme = 'dark';
                body.className = 'dark';
                themeIcon.className = 'fas fa-sun';
                themeText.textContent = 'Light';
            }

            // Reload current slide with new theme
            loadSlide(currentSlide);
        }

        // Keyboard navigation
        function handleKeyPress(event) {
            switch (event.key) {
                case 'ArrowRight':
                case ' ':
                    event.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    event.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    event.preventDefault();
                    goToSlide(1);
                    break;
                case 'End':
                    event.preventDefault();
                    goToSlide(totalSlides);
                    break;
            }
        }

        // Initialize when page loads
        window.onload = init;
    </script>
</body>

</html>