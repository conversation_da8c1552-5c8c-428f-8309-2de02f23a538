/* Slide 1 - Title Slide Styles */
.title-content {
    text-align: center;
    z-index: 2;
}

.main-title {
    font-size: 64px;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 24px;
    line-height: 1.1;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.subtitle {
    font-size: 28px;
    color: #f97316;
    font-weight: 600;
    margin-bottom: 40px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.event-info {
    background: rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    padding: 24px 40px;
    margin-bottom: 32px;
}

.event-text {
    font-size: 18px;
    color: #3b82f6;
    font-weight: 500;
}

.speaker-info {
    background: rgba(249, 115, 22, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 16px;
    padding: 20px 32px;
}

.speaker-text {
    font-size: 16px;
    color: #f97316;
    font-weight: 500;
}

.floating-icon {
    position: absolute;
    color: rgba(59, 130, 246, 0.2);
    animation: float 6s ease-in-out infinite;
}

.icon-1 {
    top: 10%;
    left: 10%;
    font-size: 40px;
    animation-delay: 0s;
}

.icon-2 {
    top: 20%;
    right: 15%;
    font-size: 32px;
    animation-delay: 2s;
}

.icon-3 {
    bottom: 15%;
    left: 15%;
    font-size: 36px;
    animation-delay: 4s;
}

.icon-4 {
    bottom: 25%;
    right: 10%;
    font-size: 28px;
    animation-delay: 1s;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-20px);
    }
}

.energy-pulse {
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    animation: pulse 4s ease-in-out infinite;
    z-index: 0;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

/* Slide 2 - The Problem Styles */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    width: 100%;
    align-items: center;
}

.problem-main-title {
    font-size: 48px;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 32px;
    line-height: 1.1;
}

.problem-text {
    font-size: 20px;
    color: #cbd5e1;
    line-height: 1.6;
    margin-bottom: 32px;
}

.scenario-card {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
}

.scenario-title {
    font-size: 18px;
    font-weight: 700;
    color: #ef4444;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.scenario-text {
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.5;
}

.visual-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.factory-visual {
    background: rgba(59, 130, 246, 0.1);
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    margin-bottom: 32px;
    width: 100%;
}

.factory-icon {
    font-size: 80px;
    color: #3b82f6;
    margin-bottom: 20px;
    animation: spin 8s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.factory-title {
    font-size: 24px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 12px;
}

.factory-subtitle {
    font-size: 16px;
    color: #94a3b8;
}

.stats-row {
    display: flex;
    justify-content: space-between;
    gap: 20px;
}

.stat-box {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    flex: 1;
}

.stat-number {
    font-size: 32px;
    font-weight: 800;
    color: #f97316;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: #cbd5e1;
    font-weight: 500;
}

.highlight {
    color: #f97316;
    font-weight: 700;
}

/* Slide 3 - Speaker Intro Styles */
.slide-3 .main-title {
    font-size: 48px;
    line-height: 1.2;
    margin-bottom: 20px;
}

.intro-text {
    font-size: 18px;
    color: #cbd5e1;
    line-height: 1.5;
    margin-bottom: 24px;
}

.credentials-card {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
}

.credential-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.credential-item:last-child {
    margin-bottom: 0;
}

.credential-icon {
    font-size: 20px;
    color: #3b82f6;
    margin-right: 12px;
    width: 28px;
    text-align: center;
}

.credential-text {
    font-size: 16px;
    color: #f1f5f9;
    font-weight: 500;
}

.mistakes-section {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 16px;
    padding: 24px;
}

.mistakes-title {
    font-size: 18px;
    font-weight: 700;
    color: #ef4444;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.mistake-item {
    font-size: 14px;
    color: #f1f5f9;
    line-height: 1.4;
    margin-bottom: 10px;
    padding-left: 16px;
    position: relative;
}

.mistake-item:before {
    content: "•";
    color: #ef4444;
    position: absolute;
    left: 0;
}

.mistake-item:last-child {
    margin-bottom: 0;
}

.profile-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.1);
    border: 2px solid rgba(59, 130, 246, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    color: #3b82f6;
    margin-bottom: 20px;
}

.profile-name {
    font-size: 28px;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 8px;
}

.profile-title {
    font-size: 16px;
    color: #f97316;
    font-weight: 600;
    margin-bottom: 20px;
}

.fun-fact {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 12px;
    padding: 16px;
    margin-top: 20px;
}

.fun-fact-text {
    font-size: 13px;
    color: #10b981;
    text-align: center;
    font-style: italic;
    line-height: 1.4;
}

/* Slide 4 - Quick Poll Styles */
.poll-main-title {
    font-size: 56px;
    font-weight: 800;
    color: #ffffff;
    text-align: center;
    margin-bottom: 48px;
    line-height: 1.1;
}

.poll-container {
    background: rgba(59, 130, 246, 0.1);
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: 24px;
    padding: 48px;
    width: 100%;
    max-width: 900px;
    text-align: center;
}

.poll-question {
    font-size: 28px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 32px;
    line-height: 1.3;
}

.poll-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.poll-option {
    background: rgba(249, 115, 22, 0.1);
    border: 2px solid rgba(249, 115, 22, 0.3);
    border-radius: 16px;
    padding: 32px 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.poll-option:hover {
    background: rgba(249, 115, 22, 0.2);
    border-color: rgba(249, 115, 22, 0.5);
    transform: translateY(-4px);
}

.option-letter {
    background: #f97316;
    color: white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 20px;
    font-weight: 700;
}

.option-text {
    font-size: 18px;
    color: #f1f5f9;
    font-weight: 600;
    line-height: 1.4;
}

.poll-instruction {
    font-size: 20px;
    color: #cbd5e1;
    margin-bottom: 24px;
    font-weight: 500;
}

.engagement-note {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin-top: 32px;
}

.engagement-text {
    font-size: 16px;
    color: #10b981;
    text-align: center;
    font-style: italic;
}

.floating-emoji {
    position: absolute;
    font-size: 32px;
    animation: float 4s ease-in-out infinite;
}

.emoji-1 {
    top: 15%;
    left: 10%;
    animation-delay: 0s;
}

.emoji-2 {
    top: 20%;
    right: 10%;
    animation-delay: 2s;
}

.emoji-3 {
    bottom: 20%;
    left: 15%;
    animation-delay: 1s;
}

.emoji-4 {
    bottom: 15%;
    right: 15%;
    animation-delay: 3s;
}

/* Light theme adjustments */
body.light .main-title {
    color: #1e293b;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

body.light .subtitle {
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

body.light .floating-icon {
    color: rgba(59, 130, 246, 0.3);
}

body.light .energy-pulse {
    background: radial-gradient(circle, rgba(59, 130, 246, 0.15) 0%, transparent 70%);
}

body.light .problem-main-title {
    color: #1e293b;
}

body.light .problem-text {
    color: #64748b;
}

body.light .scenario-text {
    color: #1e293b;
}

body.light .stat-label {
    color: #64748b;
}

body.light .intro-main-title {
    color: #1e293b;
}

body.light .intro-text h3 {
    color: #1e293b;
}

body.light .intro-text p {
    color: #64748b;
}

body.light .poll-main-title {
    color: #1e293b;
}

body.light .option-text {
    color: #1e293b;
}

body.light .poll-instruction {
    color: #64748b;
}

/* Slide 5 - Factory Signs Styles */
.factory-signs-title {
    font-size: 56px;
    font-weight: 800;
    color: #ffffff;
    text-align: center;
    margin-bottom: 24px;
    line-height: 1.1;
}

.factory-signs-subtitle {
    font-size: 24px;
    color: #cbd5e1;
    text-align: center;
    margin-bottom: 48px;
    font-weight: 500;
}

.warning-signs {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 48px;
}

.sign-card {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.sign-card:hover {
    background: rgba(239, 68, 68, 0.2);
    transform: translateY(-4px);
}

.sign-icon {
    font-size: 32px;
    margin-bottom: 16px;
}

.sign-title {
    font-size: 18px;
    font-weight: 700;
    color: #ef4444;
    margin-bottom: 12px;
}

.sign-description {
    font-size: 14px;
    color: #f1f5f9;
    line-height: 1.4;
}

.bottom-section {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 20px;
    padding: 32px;
    text-align: center;
}

.bottom-title {
    font-size: 24px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.bottom-text {
    font-size: 18px;
    color: #cbd5e1;
    line-height: 1.5;
}

/* Slide 6 - Real Cost Styles */
.cost-main-title {
    font-size: 48px;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 32px;
    line-height: 1.1;
}

.cost-intro-text {
    font-size: 20px;
    color: #cbd5e1;
    line-height: 1.6;
    margin-bottom: 32px;
}

.cost-examples {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 32px;
}

.cost-item {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 12px;
    padding: 20px;
}

.cost-title {
    font-size: 18px;
    font-weight: 700;
    color: #ef4444;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cost-description {
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.4;
}

.price-tag {
    background: rgba(59, 130, 246, 0.1);
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    margin-bottom: 32px;
}

.price-amount {
    font-size: 64px;
    font-weight: 800;
    color: #3b82f6;
    margin-bottom: 8px;
}

.price-label {
    font-size: 18px;
    color: #94a3b8;
    font-weight: 500;
}

.impact-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

/* Slide 7 - Case Study Styles */
.case-study-title {
    font-size: 56px;
    font-weight: 800;
    color: #ffffff;
    text-align: center;
    margin-bottom: 24px;
    line-height: 1.1;
}

.case-study-subtitle {
    font-size: 24px;
    color: #cbd5e1;
    text-align: center;
    margin-bottom: 48px;
    font-weight: 500;
}

.story-container {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 32px;
}

.story-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

.story-icon {
    font-size: 32px;
    color: #3b82f6;
    margin-right: 16px;
}

.story-title {
    font-size: 24px;
    font-weight: 700;
    color: #3b82f6;
}

.story-content {
    font-size: 18px;
    color: #f1f5f9;
    line-height: 1.6;
    margin-bottom: 24px;
}

.story-stats {
    display: flex;
    justify-content: space-around;
    margin: 32px 0;
}

.story-stat {
    text-align: center;
}

.lesson-learned {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 16px;
    padding: 32px;
    text-align: center;
}

.lesson-title {
    font-size: 24px;
    font-weight: 700;
    color: #f97316;
    margin-bottom: 16px;
}

.lesson-text {
    font-size: 18px;
    color: #f1f5f9;
    line-height: 1.6;
}

.timeline {
    display: flex;
    justify-content: space-between;
    margin: 32px 0;
    gap: 20px;
}

.timeline-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    flex: 1;
}

.timeline-step {
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin: 0 auto 12px;
}

.timeline-title {
    font-size: 16px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 8px;
}

.timeline-text {
    font-size: 14px;
    color: #f1f5f9;
    line-height: 1.4;
}

/* Slide 8 - Why We Fall Styles */
.why-fall-title {
    font-size: 48px;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 32px;
    line-height: 1.1;
}

.why-fall-intro {
    font-size: 20px;
    color: #cbd5e1;
    line-height: 1.6;
    margin-bottom: 32px;
}

.reasons-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 32px;
}

.reason-item {
    display: flex;
    align-items: center;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 12px;
    padding: 20px;
}

.reason-icon {
    font-size: 24px;
    margin-right: 20px;
    min-width: 40px;
}

.reason-title {
    font-size: 18px;
    font-weight: 700;
    color: #ef4444;
    margin-bottom: 4px;
}

.reason-description {
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.4;
}

.brain-visual {
    background: rgba(59, 130, 246, 0.1);
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    margin-bottom: 32px;
    width: 100%;
}

.brain-icon {
    font-size: 80px;
    color: #3b82f6;
    margin-bottom: 20px;
    animation: pulse 3s ease-in-out infinite;
}

.brain-title {
    font-size: 24px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 12px;
}

.brain-subtitle {
    font-size: 16px;
    color: #94a3b8;
}

.psychology-facts {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    width: 100%;
}

.fact-box {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 12px;
    padding: 16px;
    text-align: center;
}

.fact-title {
    font-size: 14px;
    font-weight: 700;
    color: #10b981;
    margin-bottom: 8px;
}

.fact-text {
    font-size: 12px;
    color: #cbd5e1;
    line-height: 1.3;
}

/* Slide 9 - Solution Framework Styles */

.pillars-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
    margin-bottom: 32px;
}

.pillar-card {
    background: rgba(59, 130, 246, 0.1);
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: 20px;
    padding: 32px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.pillar-card:hover {
    background: rgba(59, 130, 246, 0.2);
    transform: translateY(-8px);
}

.pillar-number {
    background: #f97316;
    color: white;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -10px;
    right: -10px;
    font-weight: 700;
    font-size: 16px;
}

.pillar-icon {
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 32px;
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.pillar-title {
    font-size: 20px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 12px;
}

.pillar-description {
    font-size: 14px;
    color: #cbd5e1;
    line-height: 1.4;
}

.transformation-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 32px 0;
}

.arrow-content {
    background: rgba(16, 185, 129, 0.1);
    border: 2px solid rgba(16, 185, 129, 0.3);
    border-radius: 16px;
    padding: 20px 40px;
    display: flex;
    align-items: center;
    gap: 16px;
}

.arrow-text {
    font-size: 18px;
    font-weight: 700;
    color: #10b981;
}

.arrow-icon {
    font-size: 24px;
    color: #10b981;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {

    0%,
    100% {
        transform: translateX(0);
    }

    50% {
        transform: translateX(8px);
    }
}

.bottom-message {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
}

.bottom-text {
    font-size: 18px;
    color: #f97316;
    font-weight: 600;
    line-height: 1.5;
}

/* Slide 10 - Pillar Discovery Styles */
.pillar-detail-title {
    font-size: 56px;
    font-weight: 800;
    color: #ffffff;
    text-align: center;
    margin-bottom: 24px;
    line-height: 1.1;
}

.pillar-detail-subtitle {
    font-size: 24px;
    color: #cbd5e1;
    text-align: center;
    margin-bottom: 48px;
    font-weight: 500;
}

.discovery-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 48px;
}

.discovery-section {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    padding: 32px;
}

.discovery-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

.discovery-icon {
    font-size: 24px;
    margin-right: 12px;
}

.discovery-label {
    font-size: 20px;
    font-weight: 700;
    color: #3b82f6;
}

.discovery-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.discovery-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.4;
}

.technique-box {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 16px;
    padding: 32px;
}

.technique-title {
    font-size: 24px;
    font-weight: 700;
    color: #f97316;
    margin-bottom: 24px;
    text-align: center;
}

.technique-example {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;
}

.why-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.4;
}

.technique-insight {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    font-size: 18px;
    color: #10b981;
    font-weight: 600;
}

/* Slide 11 - Interactive Exercise Styles */
.exercise-title {
    font-size: 56px;
    font-weight: 800;
    color: #ffffff;
    text-align: center;
    margin-bottom: 24px;
    line-height: 1.1;
}

.exercise-subtitle {
    font-size: 24px;
    color: #cbd5e1;
    text-align: center;
    margin-bottom: 48px;
    font-weight: 500;
}

.exercise-scenario {
    background: rgba(59, 130, 246, 0.1);
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 32px;
}

.scenario-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.scenario-icon {
    font-size: 24px;
    margin-right: 12px;
}

.scenario-title {
    font-size: 20px;
    font-weight: 700;
    color: #3b82f6;
}

.scenario-content {
    font-size: 18px;
    color: #f1f5f9;
    line-height: 1.6;
    font-style: italic;
}

.exercise-questions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    margin-bottom: 32px;
}

.question-section,
.discovery-section {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 16px;
    padding: 24px;
}

.question-title,
.discovery-title {
    font-size: 20px;
    font-weight: 700;
    color: #f97316;
    margin-bottom: 16px;
}

.question-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.question-item {
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.4;
    padding: 8px 0;
}

.discovery-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.discovery-option {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
}

.option-title {
    font-size: 16px;
    font-weight: 600;
    color: #f97316;
    margin-bottom: 4px;
}

.option-text {
    font-size: 14px;
    color: #f1f5f9;
    line-height: 1.4;
}

.exercise-takeaway {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
}

.takeaway-title {
    font-size: 20px;
    font-weight: 700;
    color: #10b981;
    margin-bottom: 12px;
}

.takeaway-text {
    font-size: 18px;
    color: #f1f5f9;
    line-height: 1.6;
}

/* Slide 12 - Pillar Development Styles */
.smart-build-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.build-principle {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    padding: 32px;
    text-align: center;
}

.principle-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.principle-title {
    font-size: 24px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 12px;
}

.principle-description {
    font-size: 18px;
    color: #f1f5f9;
    line-height: 1.6;
}

.testing-ladder {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 16px;
    padding: 32px;
}

.ladder-title {
    font-size: 24px;
    font-weight: 700;
    color: #f97316;
    text-align: center;
    margin-bottom: 24px;
}

.ladder-steps {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.ladder-step {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
}

.step-number {
    background: #f97316;
    color: white;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 20px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-title {
    font-size: 18px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 4px;
}

.step-description {
    font-size: 16px;
    color: #cbd5e1;
    margin-bottom: 4px;
}

.step-cost {
    font-size: 14px;
    color: #94a3b8;
    font-style: italic;
}

.smart-metrics {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 16px;
    padding: 32px;
}

.metrics-title {
    font-size: 24px;
    font-weight: 700;
    color: #10b981;
    text-align: center;
    margin-bottom: 24px;
}

.metrics-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.metrics-column {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
}

.metrics-header {
    font-size: 18px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 16px;
    padding: 8px;
    border-radius: 8px;
}

.metrics-header.bad {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.metrics-header.good {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.metrics-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.metric-item {
    font-size: 16px;
    color: #f1f5f9;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

/* Slide 13 - Pillar Optimization Styles */
.optimization-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.optimization-cycle {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    padding: 32px;
}

.cycle-title {
    font-size: 24px;
    font-weight: 700;
    color: #3b82f6;
    text-align: center;
    margin-bottom: 24px;
}

.cycle-steps {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 16px;
}

.cycle-step {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    flex: 1;
    min-width: 150px;
}

.step-icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.cycle-arrow {
    font-size: 24px;
    color: #3b82f6;
    font-weight: bold;
}

.optimization-examples {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 16px;
    padding: 32px;
}

.example-title {
    font-size: 24px;
    font-weight: 700;
    color: #f97316;
    text-align: center;
    margin-bottom: 24px;
}

.example-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.example-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.example-metric {
    font-size: 32px;
    font-weight: 800;
    color: #10b981;
    margin-bottom: 8px;
}

.example-description {
    font-size: 14px;
    color: #f1f5f9;
    line-height: 1.4;
}

.optimization-mindset {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 16px;
    padding: 32px;
    text-align: center;
}

.mindset-title {
    font-size: 24px;
    font-weight: 700;
    color: #10b981;
    margin-bottom: 16px;
}

.mindset-text {
    font-size: 18px;
    color: #f1f5f9;
    line-height: 1.6;
}

/* Slide 14 - Making It Stick Styles */
.stick-title {
    font-size: 56px;
    font-weight: 800;
    color: #ffffff;
    text-align: center;
    margin-bottom: 24px;
    line-height: 1.1;
}

.stick-subtitle {
    font-size: 24px;
    color: #cbd5e1;
    text-align: center;
    margin-bottom: 48px;
    font-weight: 500;
}

.change-strategy {
    display: flex;
    flex-direction: column;
    gap: 40px;
    margin-bottom: 32px;
}

.strategy-section {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    padding: 32px;
}

.strategy-title {
    font-size: 24px;
    font-weight: 700;
    color: #3b82f6;
    text-align: center;
    margin-bottom: 24px;
}

.strategy-steps {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.strategy-step {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
}

.step-number {
    background: #3b82f6;
    color: white;
    border-radius: 8px;
    padding: 4px 12px;
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 12px;
    display: inline-block;
}

.step-content .step-title {
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 8px;
}

.step-content .step-description {
    font-size: 14px;
    color: #cbd5e1;
    line-height: 1.4;
}

.culture-changes {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 16px;
    padding: 32px;
}

.culture-title {
    font-size: 24px;
    font-weight: 700;
    color: #f97316;
    text-align: center;
    margin-bottom: 24px;
}

.culture-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.culture-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
}

.culture-icon {
    font-size: 24px;
    margin-right: 16px;
    flex-shrink: 0;
}

.culture-text {
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.4;
}

.stick-reminder {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
}

.reminder-title {
    font-size: 20px;
    font-weight: 700;
    color: #10b981;
    margin-bottom: 12px;
}

.reminder-text {
    font-size: 18px;
    color: #f1f5f9;
    line-height: 1.6;
}

/* Slide 15 - Your Plan Styles */
.plan-title {
    font-size: 56px;
    font-weight: 800;
    color: #ffffff;
    text-align: center;
    margin-bottom: 24px;
    line-height: 1.1;
}

.plan-subtitle {
    font-size: 24px;
    color: #cbd5e1;
    text-align: center;
    margin-bottom: 48px;
    font-weight: 500;
}

.action-plan {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    margin-bottom: 32px;
}

.plan-week {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    padding: 24px;
}

.week-header {
    margin-bottom: 16px;
}

.week-number {
    background: #3b82f6;
    color: white;
    border-radius: 8px;
    padding: 4px 12px;
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 8px;
    display: inline-block;
}

.week-title {
    font-size: 18px;
    font-weight: 700;
    color: #3b82f6;
}

.week-tasks {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.task-item {
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.4;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

.plan-resources {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 16px;
    padding: 32px;
    text-align: center;
}

.resources-title {
    font-size: 24px;
    font-weight: 700;
    color: #f97316;
    margin-bottom: 20px;
}

.resources-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.resource-item {
    font-size: 16px;
    color: #f1f5f9;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

/* Slide 16 - Let's Connect Styles */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.connect-content {
    text-align: center;
    z-index: 2;
    position: relative;
}

.connect-title {
    font-size: 64px;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 24px;
    line-height: 1.1;
}

.connect-subtitle {
    font-size: 28px;
    color: #f97316;
    font-weight: 600;
    margin-bottom: 48px;
}

.connect-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    margin-bottom: 48px;
}

.connect-card {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    padding: 24px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.connect-card:hover {
    background: rgba(59, 130, 246, 0.2);
    transform: translateY(-4px);
}

.connect-icon {
    font-size: 32px;
    color: #3b82f6;
    margin-right: 20px;
    flex-shrink: 0;
}

.connect-info {
    text-align: left;
}

.connect-platform {
    font-size: 18px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 4px;
}

.connect-handle {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 4px;
}

.connect-description {
    font-size: 14px;
    color: #cbd5e1;
    line-height: 1.4;
}

.final-message {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 32px;
}

.message-title {
    font-size: 24px;
    font-weight: 700;
    color: #f97316;
    margin-bottom: 16px;
}

.message-text {
    font-size: 20px;
    color: #f1f5f9;
    line-height: 1.6;
}

.thank-you {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 16px;
    padding: 32px;
}

.thank-title {
    font-size: 48px;
    font-weight: 800;
    color: #10b981;
    margin-bottom: 16px;
}

.thank-subtitle {
    font-size: 24px;
    color: #10b981;
    font-weight: 600;
}

/* Light theme adjustments for all slides */
body.light .factory-signs-title,
body.light .cost-main-title,
body.light .case-study-title,
body.light .why-fall-title,
body.light .framework-title,
body.light .pillar-detail-title,
body.light .exercise-title,
body.light .stick-title,
body.light .plan-title,
body.light .connect-title {
    color: #1e293b;
}

body.light .factory-signs-subtitle,
body.light .case-study-subtitle,
body.light .framework-subtitle,
body.light .pillar-detail-subtitle,
body.light .exercise-subtitle,
body.light .stick-subtitle,
body.light .plan-subtitle {
    color: #64748b;
}

body.light .sign-description,
body.light .cost-description,
body.light .story-content,
body.light .reason-text,
body.light .pillar-description,
body.light .discovery-item,
body.light .why-item,
body.light .scenario-content,
body.light .question-item,
body.light .option-text,
body.light .takeaway-text,
body.light .principle-description,
body.light .step-description,
body.light .metric-item,
body.light .example-description,
body.light .mindset-text,
body.light .culture-text,
body.light .reminder-text,
body.light .task-item,
body.light .resource-item,
body.light .connect-description,
body.light .message-text {
    color: #1e293b;
}

body.light .bottom-text,
body.light .cost-intro-text,
body.light .why-fall-intro {
    color: #64748b;
}

body.light .connect-handle,
body.light .step-title,
body.light .step-content .step-title {
    color: #1e293b;
}

/* Comprehensive Light Theme Fixes */

/* Text content that should be dark on light background */
body.light .intro-text,
body.light .credential-text,
body.light .mistake-item,
body.light .fun-fact-text,
body.light .sign-description,
body.light .cost-description,
body.light .story-content,
body.light .lesson-text,
body.light .timeline-text,
body.light .reason-description,
body.light .fact-text,
body.light .question-text,
body.light .scenario-text,
body.light .timer-text,
body.light .principle-description,
body.light .step-description,
body.light .example-text,
body.light .mindset-description,
body.light .story-text,
body.light .change-description,
body.light .week-action,
body.light .motivation-text,
body.light .support-text,
body.light .connect-description,
body.light .final-text,
body.light .before-text,
body.light .after-text,
body.light .framework-text {
    color: #1e293b !important;
}

/* Subtitles and secondary text */
body.light .subtitle,
body.light .poll-instruction,
body.light .bottom-text,
body.light .cost-intro-text,
body.light .why-fall-intro,
body.light .brain-subtitle,
body.light .exercise-instruction,
body.light .challenge-description {
    color: #64748b !important;
}

/* Green text that needs better contrast */
body.light .fact-title,
body.light .example-title,
body.light .arrow-text,
body.light .engagement-text,
body.light .fun-fact-text {
    color: #059669 !important;
    /* Darker green for better contrast */
}

/* Blue text adjustments */
body.light .brain-title,
body.light .pillar-title,
body.light .question-title,
body.light .principle-title,
body.light .mindset-title,
body.light .exercise-title,
body.light .challenge-title,
body.light .week-title,
body.light .connect-title,
body.light .category-title,
body.light .bottom-title,
body.light .motivation-title,
body.light .support-title,
body.light .final-title {
    color: #1d4ed8 !important;
    /* Darker blue for better contrast */
}

/* Orange text adjustments */
body.light .highlight,
body.light .bottom-text,
body.light .lesson-title,
body.light .framework-title {
    color: #ea580c !important;
    /* Darker orange for better contrast */
}

/* Red text adjustments */
body.light .cost-title,
body.light .sign-title,
body.light .reason-title,
body.light .mistakes-title {
    color: #dc2626 !important;
    /* Darker red for better contrast */
}

/* White text that should be dark in light theme */
body.light .profile-name,
body.light .change-title,
body.light .week-number,
body.light .question-number,
body.light .step-number {
    color: #1e293b !important;
}

/* Contact and special text elements */
body.light .contact-text {
    color: #1d4ed8 !important;
    /* Dark blue for contact info */
}

/* Arrow icons and special elements */
body.light .arrow-icon {
    color: #059669 !important;
    /* Darker green for arrows */
}

/* Timeline and step elements */
body.light .timeline-title {
    background: transparent !important;
    /* Remove blue background */
    color: #1d4ed8 !important;
}

body.light .timeline-step {
    background: #3b82f6 !important;
    /* Same blue as other slides */
    color: white !important;
}

/* Profile title adjustments */
body.light .profile-title {
    color: #ea580c !important;
    /* Darker orange */
}

/* Scenario and exercise text */
body.light .scenario-title {
    color: #ea580c !important;
    /* Darker orange */
}

/* Timer section text */
body.light .timer-title {
    color: #059669 !important;
    /* Darker green */
}

/* Light Theme Container Background Fixes */

/* Slide 7 - Remove blue background behind text and make containers visible */
body.light .story-section {
    background: rgba(248, 250, 252, 0.8) !important;
    border: 1px solid rgba(148, 163, 184, 0.3) !important;
}

body.light .story-title {
    background: transparent !important;
    /* Remove blue background */
    color: #1d4ed8 !important;
}

body.light .timeline-title {
    background: transparent !important;
    /* Remove blue background */
    color: #1d4ed8 !important;
}

body.light .timeline-item {
    background: rgba(241, 245, 249, 0.9) !important;
    border: 1px solid rgba(148, 163, 184, 0.4) !important;
}

/* Slide 10 - Magic questions containers */
body.light .question-item {
    background: rgba(59, 130, 246, 0.15) !important;
    border: 1px solid rgba(59, 130, 246, 0.3) !important;
}

body.light .before-after {
    background: rgba(241, 245, 249, 0.9) !important;
    border: 1px solid rgba(148, 163, 184, 0.3) !important;
}

body.light .framework-box {
    background: rgba(249, 115, 22, 0.15) !important;
    border: 1px solid rgba(249, 115, 22, 0.4) !important;
}

/* Slide 11 - Exercise containers */
body.light .exercise-container {
    background: rgba(59, 130, 246, 0.15) !important;
    border: 2px solid rgba(59, 130, 246, 0.4) !important;
}

body.light .scenario-box {
    background: rgba(249, 115, 22, 0.15) !important;
    border: 1px solid rgba(249, 115, 22, 0.4) !important;
}

body.light .question-card {
    background: rgba(241, 245, 249, 0.9) !important;
    border: 1px solid rgba(148, 163, 184, 0.4) !important;
}

body.light .timer-section {
    background: rgba(16, 185, 129, 0.15) !important;
    border: 1px solid rgba(16, 185, 129, 0.4) !important;
}

/* Slide 12 - Principle containers */
body.light .principle-item {
    background: rgba(59, 130, 246, 0.15) !important;
    border: 1px solid rgba(59, 130, 246, 0.3) !important;
}

body.light .cycle-visual {
    background: rgba(249, 115, 22, 0.15) !important;
    border: 2px solid rgba(249, 115, 22, 0.4) !important;
}

body.light .example-box {
    background: rgba(16, 185, 129, 0.15) !important;
    border: 1px solid rgba(16, 185, 129, 0.4) !important;
}

/* Slide 13 - Optimization containers */
body.light .mindset-item {
    background: rgba(59, 130, 246, 0.15) !important;
    border: 1px solid rgba(59, 130, 246, 0.3) !important;
}

body.light .optimization-visual {
    background: rgba(249, 115, 22, 0.15) !important;
    border: 2px solid rgba(249, 115, 22, 0.4) !important;
}

body.light .success-story {
    background: rgba(16, 185, 129, 0.15) !important;
    border: 1px solid rgba(16, 185, 129, 0.4) !important;
}

body.light .cycle-step {
    background: rgba(241, 245, 249, 0.9) !important;
    border: 1px solid rgba(148, 163, 184, 0.3) !important;
}

/* Slide 14 - Making it stick containers */
body.light .change-category {
    background: rgba(59, 130, 246, 0.15) !important;
    border: 1px solid rgba(59, 130, 246, 0.3) !important;
}

body.light .change-item {
    background: rgba(248, 250, 252, 0.9) !important;
    border: 1px solid rgba(148, 163, 184, 0.3) !important;
}

/* Slide 15 - Challenge containers */
body.light .challenge-container {
    background: rgba(59, 130, 246, 0.15) !important;
    border: 2px solid rgba(59, 130, 246, 0.4) !important;
}

body.light .week-card {
    background: rgba(248, 250, 252, 0.9) !important;
    border: 1px solid rgba(148, 163, 184, 0.4) !important;
}

body.light .motivation-box,
body.light .support-box {
    background: rgba(249, 115, 22, 0.15) !important;
    border: 1px solid rgba(249, 115, 22, 0.4) !important;
}

/* Fix white numbers and text in light theme */

/* Slide 11 - Question numbers should be white on blue background */
body.light .question-number {
    color: white !important;
}

/* Slide 13 - Step numbers should be white on orange background */
body.light .step-number {
    color: white !important;
}

/* Slide 13 - Fix white text in optimization loop */
body.light .step-text {
    color: #1e293b !important;
    /* Dark text instead of white */
}

/* Slide 15 - Week numbers should be white on blue background */
body.light .week-number {
    color: white !important;
}

/* Light Theme Navigation Controls Improvements */
body.light .slide-nav {
    background: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid rgba(148, 163, 184, 0.3) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

body.light .nav-button {
    background: rgba(59, 130, 246, 0.1) !important;
    color: #1d4ed8 !important;
    border: 1px solid rgba(59, 130, 246, 0.2) !important;
}

body.light .nav-button:hover {
    background: rgba(59, 130, 246, 0.2) !important;
    color: #1d4ed8 !important;
}

body.light .nav-button:disabled {
    background: rgba(148, 163, 184, 0.1) !important;
    color: #94a3b8 !important;
    border: 1px solid rgba(148, 163, 184, 0.2) !important;
}

body.light .slide-counter {
    color: #1e293b !important;
    background: rgba(248, 250, 252, 0.8) !important;
    border: 1px solid rgba(148, 163, 184, 0.2) !important;
}

body.light .controls {
    background: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid rgba(148, 163, 184, 0.3) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

body.light .control-btn {
    background: rgba(59, 130, 246, 0.1) !important;
    color: #1d4ed8 !important;
    border: 1px solid rgba(59, 130, 246, 0.2) !important;
}

body.light .control-btn:hover {
    background: rgba(59, 130, 246, 0.2) !important;
    color: #1d4ed8 !important;
}

body.light .control-btn.active {
    background: #3b82f6 !important;
    color: white !important;
}

/* Updated Slide 10 - Pillar Discovery Styles */
.magic-questions {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 32px;
}

.question-item {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 12px;
    padding: 20px;
}

.question-title {
    font-size: 18px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.question-text {
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.4;
}

.transformation-visual {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 32px;
}

.before-after {
    display: flex;
    align-items: center;
    gap: 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 16px;
}

.before,
.after {
    flex: 1;
    text-align: center;
}

.before-title,
.after-title {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 8px;
}

.before-title {
    color: #ef4444;
}

.after-title {
    color: #10b981;
}

.before-text,
.after-text {
    font-size: 14px;
    color: #f1f5f9;
    line-height: 1.4;
}

.arrow {
    font-size: 20px;
    color: #f97316;
}

.framework-box {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.framework-title {
    font-size: 18px;
    font-weight: 700;
    color: #f97316;
    margin-bottom: 12px;
}

.framework-text {
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.5;
    font-style: italic;
}

/* Updated Slide 11 - Interactive Exercise Styles */
.floating-timer {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(239, 68, 68, 0.2);
    border: 2px solid rgba(239, 68, 68, 0.5);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 700;
    color: #ef4444;
    animation: pulse 2s infinite;
}

.exercise-container {
    background: rgba(59, 130, 246, 0.1);
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: 20px;
    padding: 32px;
    margin-bottom: 32px;
}

.exercise-header {
    text-align: center;
    margin-bottom: 32px;
}

.exercise-title {
    font-size: 24px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 12px;
}

.exercise-instruction {
    font-size: 18px;
    color: #cbd5e1;
    line-height: 1.5;
}

.scenario-box {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 32px;
    text-align: center;
}

.scenario-title {
    font-size: 20px;
    font-weight: 700;
    color: #f97316;
    margin-bottom: 16px;
}

.scenario-text {
    font-size: 18px;
    color: #f1f5f9;
    line-height: 1.6;
    font-style: italic;
}

.questions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.question-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.question-number {
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin: 0 auto 12px;
}

.timer-section {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
}

.timer-title {
    font-size: 20px;
    font-weight: 700;
    color: #10b981;
    margin-bottom: 12px;
}

.timer-text {
    font-size: 18px;
    color: #f1f5f9;
    line-height: 1.6;
}

/* Updated Slide 12 - Pillar Development Styles */
.principles-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 32px;
}

.principle-item {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 12px;
    padding: 20px;
}

.principle-title {
    font-size: 18px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.principle-description {
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.4;
}

.cycle-visual {
    background: rgba(249, 115, 22, 0.1);
    border: 2px solid rgba(249, 115, 22, 0.3);
    border-radius: 20px;
    padding: 32px;
    text-align: center;
    margin-bottom: 24px;
}

.cycle-title {
    font-size: 24px;
    font-weight: 700;
    color: #f97316;
    margin-bottom: 24px;
}

.cycle-steps {
    display: flex;
    justify-content: space-around;
    align-items: center;
    gap: 20px;
}

.cycle-step {
    text-align: center;
    flex: 1;
}

.step-icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.build-icon {
    color: #3b82f6;
}

.measure-icon {
    color: #10b981;
}

.learn-icon {
    color: #f97316;
}

.step-title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 8px;
}

.build-title {
    color: #3b82f6;
}

.measure-title {
    color: #10b981;
}

.learn-title {
    color: #f97316;
}

.step-description {
    font-size: 14px;
    color: #cbd5e1;
    line-height: 1.4;
}

.example-box {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.example-title {
    font-size: 18px;
    font-weight: 700;
    color: #10b981;
    margin-bottom: 12px;
}

.example-text {
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.5;
}

/* Updated Slide 13 - Pillar Optimization Styles */
.optimization-mindset {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 32px;
}

.mindset-item {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 12px;
    padding: 20px;
}

.mindset-title {
    font-size: 18px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.mindset-description {
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.4;
}

.optimization-visual {
    background: rgba(249, 115, 22, 0.1);
    border: 2px solid rgba(249, 115, 22, 0.3);
    border-radius: 20px;
    padding: 32px;
    text-align: center;
    margin-bottom: 24px;
}

.visual-title {
    font-size: 24px;
    font-weight: 700;
    color: #f97316;
    margin-bottom: 24px;
}

.improvement-cycle {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.cycle-step {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    text-align: center;
}

.step-number {
    background: #f97316;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
    margin: 0 auto 8px;
}

.step-text {
    font-size: 14px;
    color: #f1f5f9;
    line-height: 1.4;
}

.success-story {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.story-title {
    font-size: 18px;
    font-weight: 700;
    color: #10b981;
    margin-bottom: 12px;
}

.story-text {
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.5;
}

/* Updated Slide 14 - Making It Stick Styles */
.changes-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    margin-bottom: 32px;
}

.change-category {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    padding: 24px;
}

.category-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.category-icon {
    font-size: 24px;
    color: #3b82f6;
    margin-right: 12px;
}

.category-title {
    font-size: 20px;
    font-weight: 700;
    color: #3b82f6;
}

.change-items {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.change-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 16px;
}

.change-title {
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 8px;
}

.change-description {
    font-size: 14px;
    color: #cbd5e1;
    line-height: 1.4;
}

/* Updated Slide 15 - Your Plan Styles */
.challenge-container {
    background: rgba(59, 130, 246, 0.1);
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: 20px;
    padding: 32px;
    margin-bottom: 32px;
}

.challenge-header {
    text-align: center;
    margin-bottom: 32px;
}

.challenge-title {
    font-size: 24px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.challenge-description {
    font-size: 18px;
    color: #cbd5e1;
    line-height: 1.5;
}

.weeks-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.week-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.week-number {
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin: 0 auto 12px;
}

.week-title {
    font-size: 16px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 8px;
}

.week-action {
    font-size: 14px;
    color: #f1f5f9;
    line-height: 1.4;
}

.slide-15 .bottom-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    width: 100%;
}

.motivation-box,
.support-box {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.motivation-title,
.support-title {
    font-size: 18px;
    font-weight: 700;
    color: #f97316;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.motivation-text,
.support-text {
    font-size: 16px;
    color: #f1f5f9;
    line-height: 1.5;
}

/* Updated Slide 16 - Let's Connect Styles */
.content-wrapper {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.connect-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 32px;
    width: 100%;
    max-width: 900px;
}

.connect-card {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.connect-card:hover {
    background: rgba(59, 130, 246, 0.2);
    transform: translateY(-4px);
}

.connect-icon {
    font-size: 32px;
    color: #3b82f6;
    margin-bottom: 16px;
}

.connect-title {
    font-size: 18px;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 8px;
}

.connect-description {
    font-size: 14px;
    color: #cbd5e1;
    line-height: 1.4;
}

.final-message {
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 16px;
    padding: 32px;
    text-align: center;
    width: 100%;
    max-width: 800px;
}

.final-title {
    font-size: 24px;
    font-weight: 700;
    color: #f97316;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.final-text {
    font-size: 18px;
    color: #f1f5f9;
    line-height: 1.6;
    margin-bottom: 24px;
}

.contact-info {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 16px;
}

.contact-text {
    font-size: 16px;
    color: #3b82f6;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}